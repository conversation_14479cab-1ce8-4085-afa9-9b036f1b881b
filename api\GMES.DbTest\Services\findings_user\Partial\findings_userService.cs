﻿/*
 *所有关于findings_user类的业务代码应在此处编写
*可使用repository.调用常用方法，获取EF/Dapper等信息
*如果需要事务请使用repository.DbContextBeginTransaction
*也可使用DBServerProvider.手动获取数据库相关信息
*用户信息、权限、角色等使用UserContext.Current操作
*findings_userService对增、删、改查、导入、导出、审核业务代码扩展参照ServiceFunFilter
*/
using GMES.Core.BaseProvider;
using GMES.Core.Extensions.AutofacManager;
using GMES.Entity.DomainModels;
using System.Linq;
using GMES.Core.Utilities;
using System.Linq.Expressions;
using GMES.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using GMES.DbTest.IRepositories;

namespace GMES.DbTest.Services
{
    public partial class findings_userService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly Ifindings_userRepository _repository;//访问数据库

        [ActivatorUtilitiesConstructor]
        public findings_userService(
            Ifindings_userRepository dbRepository,
            IHttpContextAccessor httpContextAccessor
            )
        : base(dbRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _repository = dbRepository;
            //多租户会用到这init代码，其他情况可以不用
            //base.Init(dbRepository);
        }
  }
}
