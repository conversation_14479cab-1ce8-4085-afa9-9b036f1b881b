﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Linq;
using GMES.Core.Enums;
using GMES.Core.Extensions;
using GMES.Core.ObjectActionValidator;
using GMES.Core.Services;
using GMES.Core.Utilities;

namespace GMES.Core.Filters
{
    public class ActionExecuteFilter : IActionFilter
    {

        public void OnActionExecuting(ActionExecutingContext context)
        {
            //验证方法参数
            context.ActionParamsValidator();
        }
        public void OnActionExecuted(ActionExecutedContext context)
        {

        }
    }
}
