/*
 *Author：GTECH
 *
 *代码由框架生成,此处任何更改都可能导致被代码生成器覆盖
 *所有业务编写全部应在Partial文件夹下findings_userService与Ifindings_userService中编写
 */
using GMES.DbTest.IRepositories;
using GMES.DbTest.IServices;
using GMES.Core.BaseProvider;
using GMES.Core.Extensions.AutofacManager;
using GMES.Entity.DomainModels;

namespace GMES.DbTest.Services
{
    public partial class findings_userService : ServiceBase<findings_user, Ifindings_userRepository>
    , Ifindings_userService, IDependency
    {
    public findings_userService(Ifindings_userRepository repository)
    : base(repository)
    {
    Init(repository);
    }
    public static Ifindings_userService Instance
    {
      get { return AutofacContainerModule.GetService<Ifindings_userService>(); } }
    }
 }
