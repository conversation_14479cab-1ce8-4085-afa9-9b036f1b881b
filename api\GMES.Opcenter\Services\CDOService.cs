﻿using Camstar.XMLClient.API.Utilities;
using Camstar.XMLClient.Interface;
using Castle.Core.Resource;
using GMES.Core.Extensions;
using GMES.Opcenter.IServices;
using GMES.Opcenter.Model.Request;
using GMES.Opcenter.Model.Result;
using GMES.Core.Configuration;
using Camstar.XMLClient.API;
using Camstar.XMLClient.Enum;
using Dapper;
using System.Text.RegularExpressions;
using GMES.Core.DBManager;
using System.Text;
using GMES.Opcenter.Helps;
using System.Data;
using Newtonsoft.Json;
using System.Reflection;

using GMES.Core.Enums;
using GMES.Core.Services;
using System.Xml.Linq;
using GMES.Core.DBManage;
using GMES.Entity.SystemModels;
using GMES.Entity.DomainModels;
using static Dapper.SqlMapper;
using System.Collections.Generic;
using System.Security.Policy;
using System;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using GMES.Core.Const;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace GMES.Opcenter.Services
{
    public class CDOService : ICDOService
    {
        private ICamstarXmlClient _camstarXmlClient;
        public CDOService(ICamstarXmlClient camstarXmlClient)
        {
            _camstarXmlClient = camstarXmlClient;
        }

        public dynamic StandardTimeMaint(StandardTimeRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                { 
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "W_StandardTimeMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                switch (request.EventName)
                {
                    case "delete":
                        {
                            ICsiObject inputData = oService.InputData();
                            ICsiNamedObject objectToChange = inputData.NamedObjectField("ObjectToChange");

                            objectToChange.SetObjectType("W_StandardTime");
                            objectToChange.SetObjectId(request.CDOID);
                            objectToChange.SetRef(request.CDOName);
                            oService.Perform("Delete");
                        }
                        break;
                    default:
                        {
                            ICsiObject inputDataSync = oService.InputData();
                            inputDataSync.DataField("SyncName").SetValue(request.CDOName);
                            oService.Perform("Sync");

                            ICsiObject inputData = oService.InputData();
                            ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");

                            objectChanges.DataField("Name").SetValue(request.CDOName);
                            objectChanges.DataField("Description").SetValue(request.Description);
                            objectChanges.DataField("Notes").SetValue(request.Notes);
                            objectChanges.RevisionedObjectField("Product").SetRef(request.Product, "", true);
                            objectChanges.NamedObjectField("Resource").SetRef(request.Resource);
                            objectChanges.DataField("Capacity").SetValue(request.Capacity);
                            objectChanges.DataField("StandardTime").SetValue(request.StandardTime);
                            objectChanges.DataField("PinTime").SetValue(request.PinTime);
                            objectChanges.DataField("ChangeModelTime").SetValue(request.ChangeModelTime);
                        }
                        break;
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }
        /// <summary>
        /// 创建设备维修单
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public dynamic EquipmentRepairMaint(EquipmentRepairRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
               if (!string.IsNullOrEmpty(request.Password))
               {_camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "JobCreate";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                //ICsiNamedObject objectChanges = inputData.NamedObjectField("JobCreate");

                inputData.NamedObjectField("JobModel").SetRef(request.Jobmodel);

                inputData.NamedObjectField("JobMaintenance").SetRef(request.JobMaintenance);

                inputData.DataField("Comments").SetValue(request.Notes);
                
                inputData.NamedObjectField("Resource").SetRef(request.Resource);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }
        /// <summary>
        /// 分配设备维修单
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public dynamic EquipmentRepairAssignMaint(EquipmentRepairRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (request.requestData != null)
                {
                    foreach (var item in request.requestData)
                    {
                        string sql = @"SELECT t.JOBSTATUS FROM a_Jobtxnhistory t WHERE t.JOBORDERNAME =:jobOrderName ORDER BY t.JOBTXNHISTORYID desc";
                        DynamicParameters param = new DynamicParameters();
                        param.Add("jobOrderName", item.OrderName);
                        var jobStatus = DBServerProvider.SqlDapper.QueryFirst<string>(sql, param);
                        if (jobStatus == "INPROGRESS")
                        {
                            var clockOffResult = EquipmentRepairClockOffMaint(new EquipmentRepairRequest() { User = request.User, Password = request.Password, OrderName = item.OrderName, Resource = request.Resource ?? item.ResourceName, Name = request.Name });
                            if (clockOffResult.Result == 0)
                            {
                                return clockOffResult;
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "JobAssign";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                ICsiSubentityList technicianList = inputData.SubentityList("Technicians");
                ICsiSubentity techniciansub = technicianList.AppendItem();
                techniciansub.NamedObjectField("Technician").SetRef(request.Name);

               // inputData.SubentityList("Technician").SetRef(request.Name);
                 if (request.requestData != null)
                {
                   // ICsiSubentityList subentityList = inputData.SubentityList//("ResolveNamedObjRef");
                  foreach (var item in request.requestData)
                   {
                        // ICsiSubentity sub = subentityList.AppendItem();
                        inputData.NamedObjectField("Resource").SetRef(item.ResourceName);
                        inputData.NamedObjectField("JobOrder").SetRef(item.OrderName);
                    }
                  }


                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 确认分配设备维修单,打开维修单计算时间
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public dynamic EquipmentRepairAcknowledgeMaint(EquipmentRepairRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "JobAcknowledge";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                //ICsiNamedObject objectChanges = inputData.NamedObjectField("JobCreate");
                inputData.NamedObjectField("JobModel").SetRef(request.Jobmodel);
                //inputData.DataField("Comments").SetValue(request.Notes);
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                inputData.NamedObjectField("JobOrder").SetRef(request.OrderName);
                inputData.DataField("AutoClockOn").SetValue(request.AutoClockOn);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 打开维修单计算时间(再分配用）
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public dynamic EquipmentRepairClockOnMaint(EquipmentRepairRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "JobClockOn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                //ICsiNamedObject objectChanges = inputData.NamedObjectField("JobCreate");
                inputData.NamedObjectField("JobModel").SetRef(request.Jobmodel);
                //inputData.DataField("Comments").SetValue(request.Notes);
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                inputData.NamedObjectField("JobOrder").SetRef(request.OrderName);
                //inputData.DataField("AutoClockOn").SetValue(request.AutoClockOn);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 执行设备维修单
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public dynamic EquipmentRepairInprogressMaint(EquipmentRepairRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "JobProgress";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                //ICsiNamedObject objectChanges = inputData.NamedObjectField("JobCreate");
                inputData.NamedObjectField("JobModel").SetRef(request.Jobmodel);
                inputData.DataField("Comments").SetValue(request.Notes);
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                inputData.NamedObjectField("JobOrder").SetRef(request.OrderName);

                inputData.NamedObjectField("SymptomCode").SetRef(request.SymptomCode);
                inputData.NamedObjectField("RepairCode").SetRef(request.RepairCode);
                inputData.NamedObjectField("CauseCode").SetRef(request.CauseCode);
                //inputData.DataField("AutoClockOn").SetValue(request.AutoClockOn);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }
        /// <summary>
        /// 关闭设备维修单计算时间
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public BaseResult<string> EquipmentRepairClockOffMaint(EquipmentRepairRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "JobClockOff";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                //ICsiNamedObject objectChanges = inputData.NamedObjectField("JobCreate");
                inputData.NamedObjectField("JobModel").SetRef(request.Jobmodel);
               // inputData.DataField("Comments").SetValue(request.Notes);
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                inputData.NamedObjectField("JobOrder").SetRef(request.OrderName);
                //inputData.DataField("AutoClockOn").SetValue(request.AutoClockOn);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }
        /// <summary>
        /// 关闭设备维修单
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public dynamic EquipmentRepairCompleteMaint(EquipmentRepairRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "JobComplete";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                //ICsiNamedObject objectChanges = inputData.NamedObjectField("JobCreate");
                inputData.NamedObjectField("JobModel").SetRef(request.Jobmodel);
                inputData.DataField("Comments").SetValue(request.Notes);
                inputData.DataField("JobCore").SetValue(request.JobCore);
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                inputData.NamedObjectField("JobOrder").SetRef(request.OrderName);
                //inputData.DataField("AutoClockOn").SetValue(request.AutoClockOn);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 创批
        /// </summary>
        /// <param name=""></param>
        /// <returns></returns>
        public dynamic Start(StartRequest lotstart)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            if (lotstart == null)
            {
                result.SetFail(null, "创批数据为空！");
                return result;
            }
            if (lotstart.Qty == null)
            {
                result.SetFail(null, "数量为必填！");
                return result;
            }
            try
            {
                string sql = @"SELECT  SUM(CN.Qty) AS Qty, MAX(MO.Qty) AS MfgOrderQty
                FROM Container CN
                INNER JOIN MfgOrder MO ON CN.MfgOrderId = MO.MfgOrderId
                WHERE MO.MfgOrderName = :mfgorder
                AND CN.Status <> 2
                and cn.parentcontainerid is null
                GROUP BY MO.MfgOrderName";
                DynamicParameters param = new DynamicParameters();
                param.Add("mfgOrder", lotstart.MfgOrder);
                dynamic checkResult = DBServerProvider.SqlDapper.QueryDynamicFirst(sql, param);
                if (checkResult != null)
                {
                    float qty = 0, mfgOrderQty = 0;
                    float.TryParse(checkResult.QTY?.ToString(), out qty);
                    float.TryParse(checkResult.MFGORDERQTY?.ToString(), out mfgOrderQty);

                    if (qty + lotstart.Qty > mfgOrderQty)
                    {
                        result.SetFail(null, "超出工单可创批数量！");
                        return result;
                    }
                }

                if (!string.IsNullOrEmpty(lotstart.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(lotstart.User, lotstart.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "Start";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                //_inputData.NamedObjectField("PrintQueue").SetRef(lotstart.PrintQueue);
                

                ICsiObject _currentStatusDetails = _inputData.ObjectField("CurrentStatusDetails");
                _currentStatusDetails.NamedObjectField("Factory").SetRef(lotstart.Factory);
                //_inputData.NamedObjectField("NumberingRule").SetRef("Container Rule");

                ICsiObject _details = _inputData.ObjectField("Details");
                _details.DataField("AutoNumber").SetValue("True");
                _details.NamedObjectField("AutoNumberRule").SetRef("Container Rule");
                _details.DataField("IsContainer").SetValue("True");
                _details.NamedObjectField("MfgOrder").SetRef(lotstart.MfgOrder);
                _details.RevisionedObjectField("Product").SetRef(lotstart.Product, "", true);
                _details.DataField("Qty").SetValue(lotstart.Qty.ToString());
                oService.SetExecute();
                ICsiRequestData requestData;

                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                requestData.RequestField("Container");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();


                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    ICsiResponseData response = respDoc.GetService().ResponseData();
                    ICsiContainer container = (ICsiContainer)response.GetResponseFieldByName("Container");
                    string containerName = container.GetName();
                    string url = AppSetting.ERPConfig.COR_PRINT;
                    //获取批次信息
                    BaseResult<CommonQueryResult> containerInfo = new QueryService().GetStartLabelInfo(containerName);
                    ERPRequest<StartLabel> printRequest = new ERPRequest<StartLabel>();
                    printRequest.data = new List<StartLabel>();
                    StartLabel label = new StartLabel();
                    label.ZEINR = containerName;
                    label.AUFNR = containerInfo.Data.MfgOrder;
                    label.MATNR = containerInfo.Data.Product;
                    label.BZ_MENGE = containerInfo.Data.MfgOrderQty;
                    label.MECAP = containerInfo.Data.Qty;
                    label.STEXT = containerInfo.Data.StepName;
                    label.MEINS = containerInfo.Data.Uom;
                    label.RSRC = lotstart.PrintQueue;
                    printRequest.data.Add(label);

                    BaseResult<string> printResult = ERPPrint(url, JsonConvert.SerializeObject(printRequest));
                    if (printResult.Result == 0)
                        result.SetFail(null, printResult.Message);
                    else
                        result.SetSuccess(null, printResult.Message);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic MoveStdTxn(MoveRequest lotmove)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            DynamicParameters parameters = new DynamicParameters();


            try
            {
                string serviceName = "MoveStd";
                if (!string.IsNullOrEmpty(lotmove.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(lotmove.User, lotmove.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.ContainerField("Container").SetRef(lotmove.Container, "");
                _inputData.DataField("W_ActualWorkTime").SetValue(lotmove.ActualWorkTime.ToString());
                _inputData.NamedObjectField("Resource").SetRef(lotmove.Resource);
                _inputData.NamedObjectField("TaskContainer").SetRef(lotmove.TaskContainer);
                oService.SetExecute();
                ICsiRequestData requestData;

                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {

                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic MoveInTxn(MoveRequest lotmove)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                string serviceName = "MoveIn";
                if (!string.IsNullOrEmpty(lotmove.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(lotmove.User, lotmove.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("ClearLocation").SetValue(lotmove.ClearLocation.ToString());
                _inputData.ContainerField("Container").SetRef(lotmove.Container, "");
                _inputData.DataField("ES_SNDetail").SetValue(lotmove.ES_SNDetail);
                _inputData.DataField("ES_SNStatus").SetValue(lotmove.ES_SNStatus);
                _inputData.DataField("Factory").SetValue(lotmove.Factory);
                _inputData.NamedObjectField("Resource").SetRef(lotmove.Resource);
                _inputData.NamedObjectField("TaskContainer").SetRef(lotmove.TaskContainer);
                oService.SetExecute();
                ICsiRequestData requestData;

                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {

                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic HoldReleaseMultiple(HoldReleaseRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<List<HoldReleaseResult>> result = new BaseResult<List<HoldReleaseResult>>();
            List<HoldReleaseResult> holdReleaseResults = new();
            string msg = string.Empty;
            //string ID = Guid.NewGuid().ToString("D");
            bool isPassd = true;
            try
            {
                foreach (var item in request.Containers)
                {
                    //检查Container是否已上料，已上料的批次必须先进行下料再冻结
                    string sqlStr = @"SELECT COUNT(1) as count
                                        FROM A_ResourceComponents RC
                                        INNER JOIN ResourceDef RE ON RC.ResourceID = RE.ResourceID
                                        INNER JOIN Container CN ON RC.FromContainerId = CN.ContainerId
                                        LEFT JOIN MFGORDER MO ON CN.W_RequestMaterialMfgOrderId = MO.MfgOrderId
                                        WHERE CN.CONTAINERNAME = :ContainerName";
                    DynamicParameters parameters = new DynamicParameters();
                    parameters.Add("ContainerName", item);
                    dynamic ContainerName = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, parameters);
                    if (!string.IsNullOrEmpty(ContainerName)) {
                        result.SetFail(holdReleaseResults, "该批次已上料，需先从设备下料再冻结" + msg);
                    } 
                    else {
                        HoldReleaseResult holdReleaseResult = new HoldReleaseResult();
                        if (!string.IsNullOrEmpty(request.Password)) {
                            _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                        }
                        _camstarXmlClient.InitializeSession();
                        string serviceName = request.ServerName;
                        _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                        ICsiService oService = _camstarXmlClient.GetService();
                        ICsiObject inputData = oService.InputData();

                        var cons = inputData.ContainerList("Containers");
                        cons.AppendItem(item, "");
                        inputData.NamedObjectField("ES_ContainerTmp").SetRef(item);
                        inputData.DataField("ES_SNDetail").SetValue(request.ES_SNDetail.ToString());
                        inputData.DataField("ES_SNStatus").SetValue(request.ES_SNStatus.ToString());
                        if ("Holds".Equals(request.ServerName)) {
                            inputData.NamedObjectField("HoldReason").SetRef(request.Reason);
                        } else {
                            inputData.NamedObjectField("ReleaseReason").SetRef(request.Reason);
                        }

                        oService.SetExecute();
                        ICsiRequestData requestData;
                        requestData = oService.RequestData();
                        requestData.RequestField("CompletionMsg");
                        ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                        submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                        _camstarXmlClient.CleanUp(serviceName + "Doc");
                        if (submitResult.ResultCode != "1") {
                            isPassd = false;
                        }
                        holdReleaseResult.Container = item;
                        holdReleaseResult.Msg = submitResult.ResultMsg;
                        holdReleaseResults.Add(holdReleaseResult);
                    }
                }

            }
            catch (Exception ex)
            {
                isPassd = false;
                msg = ex.Message;
            }
            if (isPassd)
            {
                result.SetSuccess(holdReleaseResults, "执行成功！");
            }
            else
            {
                result.SetFail(holdReleaseResults, "执行失败！" + msg);
            }
            return result;
        }

        public dynamic GenerateNumber(GenerateNumberRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "YP_GenerateNumber";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                inputData.NamedObjectField("MfgOrder").SetRef(request.MfgOrder);
                inputData.RevisionedObjectField("Spec").SetRef(request.Spec, "", true);
                inputData.NamedObjectField("NumberingRule").SetRef(request.NumberingRule);

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                requestData.RequestField("NewNumber");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                ICsiResponseData responseData = respDoc.GetService().ResponseData();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    ICsiDataField newNumber = (CsiDataField)responseData.GetResponseFieldByName("NewNumber");
                    result.SetSuccess(newNumber == null ? string.Empty : newNumber.GetValue(), submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        //拆批执行
        public dynamic LotSplit(SplitLotRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            if(string.IsNullOrEmpty(request.PrintQueue))
            {
                return result.SetFail(null, "打印队列不能为空！");
            }
            if (request.Qty <= 0)
            {
                return result.SetFail(null, "拆分数量不能小于等于0！");
            }
            if(string.IsNullOrEmpty(request.Container))
            {
                return result.SetFail(null, "容器不能为空！");
            }
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "Split";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                // inputData.NamedObjectField("PrintQueue").SetRef(request.PrintQueue);
                inputData.DataField("AutoNumber").SetValue(request.AutoNumber.ToString());
                inputData.DataField("CloseWhenEmpty").SetValue(request.CloseWhenEmpty.ToString());
                inputData.ContainerField("Container").SetRef(request.Container, "");
                inputData.DataField("ES_SNDetail").SetValue(request.ES_SNDetail.ToString());
                inputData.DataField("ES_SNStatus").SetValue(request.ES_SNStatus.ToString());
                inputData.ContainerField("TaskContainer").SetRef(request.Container, "");
                inputData.DataField("Comments").SetValue(request.Comments);
                var detail = inputData.SubentityList("ToContainerDetails");
                var list = detail.AppendItem();
                list.DataField("Qty").SetValue(request.Qty.ToString());
                list.DataField("ToContainerName").SetValue("");
                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                requestData.RequestField("ToContainerDetails");
                // requestData.RequestField("ACEMessage");
                // requestData.RequestField("ACEStatus");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                ICsiResponseData responseData = respDoc.GetService().ResponseData();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    ICsiSubentityList toContainerDetails = (CsiSubentityList)responseData.GetResponseFieldByName("ToContainerDetails");
                    // ICsiDataField container = (CsiDataField)responseData.GetResponseFieldByName("ToContainerName");
                    ICsiDataField container = (CsiDataField)toContainerDetails.GetItemByIndex(0).GetField("ToContainerName");
                    string containerName = container.GetValue();
                    string url = AppSetting.ERPConfig.COR_PRINT;
                    //获取批次信息
                    BaseResult<CommonQueryResult> containerInfo = new QueryService().GetStartLabelInfo(containerName);
                    ERPRequest<StartLabel> printRequest = new ERPRequest<StartLabel>();
                    printRequest.data = new List<StartLabel>();
                    StartLabel label = new StartLabel();
                    label.ZEINR = containerName;
                    label.AUFNR = containerInfo.Data.MfgOrder;
                    label.MATNR = containerInfo.Data.Product;
                    label.BZ_MENGE = containerInfo.Data.MfgOrderQty;
                    label.MECAP = containerInfo.Data.Qty;
                    label.STEXT = containerInfo.Data.StepName;
                    label.MEINS = containerInfo.Data.Uom;
                    label.RSRC = request.PrintQueue;
                    printRequest.data.Add(label);

                    BaseResult<string> printResult = ERPPrint(url, JsonConvert.SerializeObject(printRequest));
                    if (printResult.Result == 0)
                        result.SetFail(null, "拆批成功，打印失败："+printResult.Message);
                    else
                        result.SetSuccess(null, printResult.Message);

                    // result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }

            }
            catch (Exception ex)
            {

                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic CombineTxn(CombineLotRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "Combine";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                //inputData.NamedObjectField("PrintQueue").SetRef(request.PrintQueue);
                inputData.ContainerField("Container").SetRef(request.Container, "");
                inputData.DataField("Comments").SetValue(request.Comments);
                inputData.DataField("ES_SNDetail").SetValue(request.ES_SNDetail.ToString());
                inputData.DataField("ES_SNStatus").SetValue(request.ES_SNStatus.ToString());
                inputData.DataField("Factory");
                var detail = inputData.SubentityList("FromContainerDetails");
                foreach (var item in request.ContainerDetails)
                {
                    var list = detail.AppendItem();
                    list.DataField("CloseWhenEmpty").SetValue(item.CloseWhenEmpty.ToString());
                    list.DataField("CombineAllQty").SetValue(item.CombineAllQty.ToString());
                    list.ContainerField("FromContainer").SetRef(item.FromContainer, "");
                    list.DataField("Qty").SetValue(item.Qty.ToString());
                }
                inputData.ContainerField("TaskContainer").SetRef(request.Container, "");
                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                requestData.RequestField("ACEMessage");
                requestData.RequestField("ACEStatus");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                ICsiResponseData responseData = respDoc.GetService().ResponseData();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {

                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {

                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 保存首件检验单文件信息
        /// </summary>
        /// <param name="FAI"></param>
        /// <returns></returns>
        public dynamic UploadSaveExcelFile(List<string[]> dataList, string FAITask, string FAIFileName)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                string serviceName = "YP_FAIInspectionTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("YP_FAIType").SetValue("5");
                _inputData.NamedObjectField("YP_FAITask").SetRef(FAITask);
                _inputData.DataField("YP_FileName").SetValue(FAIFileName);
                _inputData.DataField("YP_FileTitle").SetValue(dataList[0][1]);
                _inputData.DataField("YP_ProductName").SetValue(dataList[2][2]);
                _inputData.DataField("YP_ProductNumber").SetValue(dataList[2][6]);
                _inputData.DataField("YP_DrawingRev").SetValue(dataList[2][10]);
                _inputData.NamedObjectField("YP_Inspector").SetRef(dataList[2][13]);
                _inputData.DataField("YP_InspectTime").SetValue(dataList[2][16]);
                _inputData.DataField("YP_InspectionDepartment").SetValue(dataList[3][2]);
                _inputData.NamedObjectField("YP_Submitter").SetRef(dataList[3][6]);
                _inputData.DataField("YP_SubmissionTime").SetFormattedValue(dataList[3][10], Camstar.XMLClient.Enum.DataFormats.FormatDateAndTime);
                _inputData.DataField("YP_SampleQty").SetValue(dataList[3][13]);
                _inputData.DataField("YP_Remarks").SetValue(dataList[3][16]);
                ICsiSubentityList YP_FAIInspectionDetail = _inputData.SubentityList("YP_FAIInspectionDetail");
                if (dataList != null && dataList.Count > 6)
                {
                    for (int i = 6; i < dataList.Count; i++)
                    {
                        ICsiSubentity Item = YP_FAIInspectionDetail.AppendItem();
                        Item.DataField("YP_Sequence").SetValue(dataList[i][0]);
                        Item.DataField("YP_SizeNumber").SetValue(dataList[i][1]);
                        Item.DataField("YP_Specifications").SetValue(dataList[i][2]);
                        Item.DataField("YP_UpperTolerance").SetValue(dataList[i][3]);
                        Item.DataField("YP_LowerTolerance").SetValue(dataList[i][4]);
                        Item.DataField("YP_MeasurementTool").SetValue(dataList[i][5]);
                        Item.DataField("YP_MeasuredData1").SetValue(dataList[i][6]);
                        Item.DataField("YP_MeasuredData2").SetValue(dataList[i][7]);
                        Item.DataField("YP_MeasuredData3").SetValue(dataList[i][8]);
                        Item.DataField("YP_MeasuredData4").SetValue(dataList[i][9]);
                        Item.DataField("YP_MeasuredData5").SetValue(dataList[i][10]);
                        Item.DataField("YP_JudgmentResult1").SetValue(dataList[i][11]);
                        Item.DataField("YP_JudgmentResult2").SetValue(dataList[i][12]);
                        Item.DataField("YP_JudgmentResult3").SetValue(dataList[i][13]);
                        Item.DataField("YP_JudgmentResult4").SetValue(dataList[i][14]);
                        Item.DataField("YP_JudgmentResult5").SetValue(dataList[i][15]);
                        Item.DataField("YP_Remarks").SetValue(dataList[i][16]);
                    }
                }

                oService.SetExecute();
                ICsiRequestData requestData;

                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic ScrapBonus(ScrapBonusRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            if (string.IsNullOrEmpty(request.Container))
            {
                return result.SetFail(null, "批次为空！");
            }
            if (request.ServiceDetails.Count == 0)
            {
                return result.SetFail(null, "报废数据为空！");
            }
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "ChangeQty";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                inputData.ContainerField("Container").SetRef(request.Container, "");
                inputData.DataField("ES_SNDetail").SetValue(request.ES_SNDetail.ToString());
                inputData.DataField("ES_SNStatus").SetValue(request.ES_SNStatus.ToString());
                var detail = inputData.SubentityList("ServiceDetails");
                foreach (var item in request.ServiceDetails)
                {
                    var list = detail.AppendItem();
                    list.SetAttribute("__CDOTypeName", item.CDOTypeName);
                    list.DataField("EnteredQty").SetValue(item.EnteredQty.ToString());
                    list.DataField("Comments").SetValue(item.Comments);
                    list.NamedObjectField("ReasonCode").SetRef(item.ReasonCode.Name);
                    list.DataField("RecordAllQty").SetValue(item.RecordAllQty.ToString());
                    if (item.CDOTypeName == "BonusDetails")
                    {
                        list.DataField("YP_IsCustomized").SetValue(item.YP_IsCustomized.ToString());
                        list.NamedObjectField("YP_QtyLossHistoryDetails").SetObjectId(item.YP_LossDetails);
                    }

                }
                inputData.ContainerField("TaskContainer").SetRef(request.Container, "");
                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                requestData.RequestField("ACEMessage");
                requestData.RequestField("ACEStatus");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                ICsiResponseData responseData = respDoc.GetService().ResponseData();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {

                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic MaterialStart(MaterialStartRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            QueryService queryService = new QueryService();
            BaseResult<List<MaterialContainerResult>> result = new BaseResult<List<MaterialContainerResult>>();

            try
            {
                BaseResult<ContainerInfoResult> _checkContainer = queryService.GetMaterialLotInfo(request.Container);
                if (_checkContainer.Result == 0)
                    return result.SetFail(null, _checkContainer.Message);
                if (_checkContainer.Data != null)
                {
                    var requestQty = decimal.Parse(request.Qty);
                    //数量和工单一样
                    if (request.RequestMaterialMfgOrder == _checkContainer.Data.mfgOrderName && requestQty == _checkContainer.Data.qty)
                    {
                        return result.SetFail(null, request.Container + "已存在。");
                    }

                    //关闭时先打开批次
                    if (_checkContainer.Data.status == "2")
                    {
                        SubmitResult openRes = Open(request);
                        if (openRes.ResultCode == "0") return result.SetFail(null, openRes.ResultMsg);
                    }

                    //修改数量
                    if (requestQty != _checkContainer.Data.qty)
                    {
                        BaseResult<string> changeRes = ChangeQty(new ChangeQtyRequest()
                        {
                            Container = request.Container,
                            AdjustQty = (requestQty - _checkContainer.Data.qty).ToString(),
                            Reason = "物料发料",
                        });
                        if (changeRes.Result == 0) return result.SetFail(null, changeRes.Message);
                    }

                    //修改领料工单
                    if (request.RequestMaterialMfgOrder != _checkContainer.Data.mfgOrderName)
                    {
                        SubmitResult maintRes = ContainerMaint(request.Container, request.RequestMaterialMfgOrder);
                        if (maintRes.ResultCode == "0") return result.SetFail(null, maintRes.ResultMsg);
                    }
                }
                else
                {
                    string serviceName = "Start";
                    if (!string.IsNullOrEmpty(request.Password))
                    {
                        _camstarXmlClient = new CamstarXmlClient(request.User, request.Password);
                    }
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject _inputData = oService.InputData();

                    _inputData.DataField("Comments").SetValue(request.Comments);
                    ICsiObject _currentStatusDetails = _inputData.ObjectField("CurrentStatusDetails");
                    _currentStatusDetails.NamedObjectField("Location").SetRef(request.Location);
                    _currentStatusDetails.NamedObjectField("Factory").SetRef(request.Factory);
                    _currentStatusDetails.RevisionedObjectField("Workflow")
                        .SetRef(string.IsNullOrEmpty(request.Workflow) == true ? "物料工艺" : request.Workflow, "", true);

                    ICsiObject _details = _inputData.ObjectField("Details");
                    _details.DataField("IsContainer").SetValue("true");
                    _details.DataField("ContainerName").SetValue(request.Container);
                    _details.DataField("Qty").SetValue(request.Qty.ToString());
                    _details.DataField("VendorName").SetValue(request.Vendor);
                    _details.DataField("NickName").SetValue(request.VendorLot);
                    _details.DataField("W_ParentLot").SetValue(request.ParentLot);
                    _details.DataField("W_SAPLot").SetValue(request.SAPLot);
                    _details.NamedObjectField("W_RequestMaterialMfgOrder").SetRef(request.RequestMaterialMfgOrder);
                    _details.NamedObjectField("W_WorkCenter").SetRef(request.WorkCenter);
                    _details.NamedObjectField("Level").SetRef("Material");
                    _details.RevisionedObjectField("Product").SetRef(request.Product, "", true);
                    if (!string.IsNullOrEmpty(request.ExpirationDate))
                    {
                        _details.DataField("ExpirationDate").SetFormattedValue(request.ExpirationDate?.ToString(), DataFormats.FormatDateAndTime);
                    }
                    if (!string.IsNullOrEmpty(request.VendorDate))
                    {
                        _details.DataField("W_VendorDate").SetFormattedValue(request.VendorDate?.ToString(), DataFormats.FormatDateAndTime);
                    }


                    oService.SetExecute();
                    ICsiRequestData requestData = oService.RequestData();
                    requestData.RequestField("CompletionMsg");
                    requestData.RequestField("Container");
                    ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                    submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    if (submitResult.ResultCode == "1")
                    {
                        result.SetSuccess(null, submitResult.ResultMsg);
                    }
                    else
                    {
                        result.SetFail(null, submitResult.ResultMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        private SubmitResult Open(MaterialStartRequest request)
        {
            SubmitResult submitResult;
            string serviceName = "Opens";
            CamstarXmlClient xmlClient = new CamstarXmlClient();
            xmlClient.InitializeSession();
            xmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
            ICsiService oService = xmlClient.GetService();
            ICsiObject _inputData = oService.InputData();

            _inputData.DataField("Comments").SetValue(request.Comments);
            ICsiNamedObjectList _containers = _inputData.NamedObjectList("Containers");
            _containers.AppendItem(request.Container);

            oService.SetExecute();
            ICsiRequestData requestData = oService.RequestData();
            requestData.RequestField("CompletionMsg");
            ICsiDocument respDoc = xmlClient.GetDocument().Submit();

            submitResult = xmlClient.CheckForErrors(respDoc);
            xmlClient.CleanUp(serviceName + "Doc");

            return submitResult;
        }

        private SubmitResult ContainerMaint(string container, string mfgorder)
        {
            SubmitResult submitResult;
            string serviceName = "ContainerMaint";
            CamstarXmlClient xmlClient = new CamstarXmlClient();
            xmlClient.InitializeSession();
            xmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
            ICsiService oService = xmlClient.GetService();
            ICsiObject _inputData = oService.InputData();

            _inputData.NamedObjectField("Container").SetRef(container);
            ICsiSubentity sub = _inputData.SubentityField("ServiceDetail");
            sub.NamedObjectField("W_RequestMaterialMfgOrder").SetRef(mfgorder);

            oService.SetExecute();
            ICsiRequestData requestData = oService.RequestData();
            requestData.RequestField("CompletionMsg");
            ICsiDocument respDoc = xmlClient.GetDocument().Submit();

            submitResult = xmlClient.CheckForErrors(respDoc);
            xmlClient.CleanUp(serviceName + "Doc");

            return submitResult;
        }

        public dynamic CloseMultiple(MultipleRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password);
                }

                string serviceName = "Closes";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();

                _inputData.DataField("Comments").SetValue(request.Comments);
                _inputData.NamedObjectField("ChangeStatusReason").SetRef(request.Reason);

                ICsiNamedObjectList _containers = _inputData.NamedObjectList("Containers");
                foreach (var container in request.Containers)
                {
                    _containers.AppendItem(container);
                }

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic OpenMultiple(MultipleRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "Opens";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();

                _inputData.DataField("Comments").SetValue(request.Comments);
                _inputData.NamedObjectField("ChangeStatusReason").SetRef(request.Reason);

                ICsiNamedObjectList _containers = _inputData.NamedObjectList("Containers");
                foreach (var container in request.Containers)
                {
                    _containers.AppendItem(container);
                }

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic MaterialReturnTxn(MaterialReturnRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "YP_MaterialReturnTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();

                ICsiNamedObjectList _containers = _inputData.NamedObjectList("Containers");
                _containers.AppendItem(request.Container);

                ICsiSubentityList _subList = _inputData.SubentityList("MaterialReturnDetails");
                ICsiSubentity _sub = _subList.AppendItem();
                _sub.DataField("Comments").SetValue(request.Comments);
                _sub.DataField("Location").SetValue(request.Location);
                _sub.DataField("Qty").SetValue(request.Qty);
                //_sub.DataField("ReturnTime").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDateAndTime);
                _sub.NamedObjectField("Reason").SetRef(request.Reason);
                _sub.NamedObjectField("Container").SetRef(request.Container);
                _sub.NamedObjectField("Employee").SetRef(request.Employee);

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }
        /// <summary>
        /// 批次异常登记
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public dynamic LotExceptionTxn(LotExceptionRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (string.IsNullOrEmpty(request.ContainerName))
                {
                    return result.SetFail(null, "批次不能为空！");
                }
                if (string.IsNullOrEmpty(request.ExceptionReasonName))
                {
                    return result.SetFail(null, "异常原因不能为空！");
                }
                if (string.IsNullOrEmpty(request.User))
                {
                    return result.SetFail(null, "操作人员不能为空！");
                }
                _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                string serviceName = "W_LotExceptionTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.NamedObjectField("Employee").SetRef(request.EmployeeName);
                _inputData.ContainerField("Container").SetRef(request.ContainerName,null);
                //_inputData.DataField("Qty").SetValue(request.Qty);
                _inputData.NamedObjectField("HoldReason").SetRef(request.ExceptionReasonName);
                _inputData.DataField("Comments").SetValue(request.ExceptionRemark);

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    //var re = LotExceptionSend(request);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic ChangeQty(ChangeQtyRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "ChangeQty";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();

                _inputData.NamedObjectField("Container").SetRef(request.Container);
                _inputData.NamedObjectField("TaskContainer").SetRef(request.Container);
                _inputData.DataField("Comments").SetValue(request.Comments);

                ICsiSubentityList _subList = _inputData.SubentityList("ServiceDetails");
                ICsiSubentity _sub = _subList.AppendItem();
                _sub.SetObjectType("AdjustDetails");
                _sub.DataField("EnteredQty").SetValue(request.AdjustQty);
                _sub.NamedObjectField("ReasonCode").SetRef(request.Reason);

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 保存巡检检验单文件信息
        /// </summary>
        /// <param name="IPQCTask"></param>
        /// <returns></returns>
        public dynamic UploadSaveIPQCExcelFile(List<string[]> dataList, string IPQCTask, string FAIFileName)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                string serviceName = "YP_IPQCInspectionTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("YP_IPQCType").SetValue("5");
                _inputData.NamedObjectField("YP_IPQCTask").SetRef(IPQCTask);
                _inputData.DataField("YP_FileName").SetValue(FAIFileName);
                _inputData.DataField("YP_FileTitle").SetValue(dataList[0][1]);
                _inputData.DataField("YP_ProductName").SetValue(dataList[2][2]);
                _inputData.DataField("YP_ProductNumber").SetValue(dataList[2][6]);
                _inputData.DataField("YP_DrawingRev").SetValue(dataList[2][10]);
                _inputData.NamedObjectField("YP_Inspector").SetRef(dataList[2][13]);
                _inputData.DataField("YP_InspectTime").SetValue(dataList[2][16]);
                _inputData.DataField("YP_InspectionDepartment").SetValue(dataList[3][2]);
                _inputData.NamedObjectField("YP_Submitter").SetRef(dataList[3][6]);
                _inputData.DataField("YP_SubmissionTime").SetFormattedValue(dataList[3][10], Camstar.XMLClient.Enum.DataFormats.FormatDateAndTime);
                _inputData.DataField("YP_SampleQty").SetValue(dataList[3][13]);
                _inputData.DataField("YP_Remarks").SetValue(dataList[3][16]);
                ICsiSubentityList YP_IPQCInspectionDetail = _inputData.SubentityList("YP_IPQCInspectionDetail");
                if (dataList != null && dataList.Count > 6)
                {
                    for (int i = 6; i < dataList.Count; i++)
                    {
                        ICsiSubentity Item = YP_IPQCInspectionDetail.AppendItem();
                        Item.DataField("YP_Sequence").SetValue(dataList[i][0]);
                        Item.DataField("YP_SizeNumber").SetValue(dataList[i][1]);
                        Item.DataField("YP_Specifications").SetValue(dataList[i][2]);
                        Item.DataField("YP_UpperTolerance").SetValue(dataList[i][3]);
                        Item.DataField("YP_LowerTolerance").SetValue(dataList[i][4]);
                        Item.DataField("YP_MeasurementTool").SetValue(dataList[i][5]);
                        Item.DataField("YP_MeasuredData1").SetValue(dataList[i][6]);
                        Item.DataField("YP_MeasuredData2").SetValue(dataList[i][7]);
                        Item.DataField("YP_MeasuredData3").SetValue(dataList[i][8]);
                        Item.DataField("YP_MeasuredData4").SetValue(dataList[i][9]);
                        Item.DataField("YP_MeasuredData5").SetValue(dataList[i][10]);
                        Item.DataField("YP_JudgmentResult1").SetValue(dataList[i][11]);
                        Item.DataField("YP_JudgmentResult2").SetValue(dataList[i][12]);
                        Item.DataField("YP_JudgmentResult3").SetValue(dataList[i][13]);
                        Item.DataField("YP_JudgmentResult4").SetValue(dataList[i][14]);
                        Item.DataField("YP_JudgmentResult5").SetValue(dataList[i][15]);
                        Item.DataField("YP_Remarks").SetValue(dataList[i][16]);
                    }
                }

                oService.SetExecute();
                ICsiRequestData requestData;

                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic ResourceComponentSetup(ResourceComponentSetupRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "ResourceComponentSetup";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                inputData.DataField("AllowEmptyContainer").SetValue("true");
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                if (request.ServiceDetails?.Count>0)
                {
                    ICsiSubentityList serviceDetails = inputData.SubentityList("ServiceDetails");
                    foreach (var item in request.ServiceDetails)
                    {
                        ICsiSubentity listItem = serviceDetails.AppendItem();
                        listItem.NamedObjectField("FromContainer").SetRef(item.FromContainer);
                        listItem.DataField("IssueControl").SetValue(item.IssueControl);
                    }
                }
                

                //下料列表
                if (request.OutToMaterialDetails?.Count > 0)
                {
                    inputData.DataField("W_MaterialOutForExcessName").SetValue(DateTime.Now.ToString("yyMMddHHmmss"));
                    ICsiSubentityList outServiceDetails = inputData.SubentityList("OutToMaterialDetails");
                    foreach (var item in request.OutToMaterialDetails)
                    {
                        ICsiSubentity listItem = outServiceDetails.AppendItem();
                        listItem.NamedObjectField("FromContainer").SetRef(item.FromContainer);
                        listItem.DataField("IssueControl").SetValue(item.IssueControl);
                        listItem.DataField("RealQty").SetValue(item.RealQty);
                    }
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 创建抽检检验单
        /// </summary>
        /// <param name="AQL"></param>
        /// <returns></returns>
        public dynamic AQLInspectionCreateTxn(AQLInspectionRequest AQL)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                string serviceName = "YP_AQLInspectionTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.NamedObjectField("YP_Container").SetRef(AQL.Container);
                _inputData.NamedObjectField("YP_Creator").SetRef(AQL.YP_Creator);
                _inputData.DataField("YP_AQLInspectionName").SetValue(AQL.Container + DateTime.Now.ToString("yyMMddHHmmss"));
                _inputData.DataField("YP_Shift").SetValue(AQL.Shift);
                _inputData.DataField("YP_InputType").SetValue(AQL.YP_InputType);
                _inputData.DataField("YP_AQLQty").SetValue(AQL.YP_AQLQty);
                _inputData.DataField("YP_ContainerQty").SetValue(AQL.YP_ContainerQty);
                _inputData.DataField("YP_DefectRate").SetValue(AQL.YP_DefectRate);
                if (AQL.YP_JudgmentResults == "1")
                {
                    _inputData.DataField("YP_JudgmentResults").SetValue("合格");
                }
                if (AQL.YP_JudgmentResults == "2")
                {
                    _inputData.DataField("YP_JudgmentResults").SetValue("不合格");
                }
                _inputData.NamedObjectField("YP_WorkCenter").SetRef(AQL.Factory);
                _inputData.NamedObjectField("YP_MfgOrder").SetRef(AQL.MfgOrder);
                _inputData.RevisionedObjectField("YP_Spec").SetRef(AQL.Spec.Split(':')[0], AQL.Spec.Split(':')[1], false);
                _inputData.RevisionedObjectField("YP_Product").SetRef(AQL.Product, "1", false);
                _inputData.DataField("YP_ProductDescription").SetValue(AQL.ProductDescription);
                ICsiSubentityList YP_AQLInspectionDetail = _inputData.SubentityList("YP_AQLInspectionDetail");
                if (AQL.YP_AQLInspectionDetail != null && AQL.YP_AQLInspectionDetail.Count > 0)
                {
                    for (int i = 0; i < AQL.YP_AQLInspectionDetail.Count; i++)
                    {
                        ICsiSubentity Item = YP_AQLInspectionDetail.AppendItem();
                        Item.DataField("YP_DefectQty").SetValue(AQL.YP_AQLInspectionDetail[i].YP_DefectQty);
                        Item.DataField("YP_DefectRate").SetValue(AQL.YP_AQLInspectionDetail[i].YP_DefectRate);
                        Item.DataField("YP_SingleDefectRate").SetValue(AQL.YP_AQLInspectionDetail[i].YP_SingleDefectRate);
                        Item.NamedObjectField("YP_isDefectReason").SetRef(AQL.YP_AQLInspectionDetail[i].YP_isDefectReason);
                    }
                }

                oService.SetExecute();
                ICsiRequestData requestData;

                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                    if (AQL.YP_JudgmentResults == "2")
                    {
                        var re = AQLInspectFailSecd(AQL);
                        if (re.Result == 0)
                        {
                            result.SetFail(null, re.Message);
                        }
                        else
                        {
                            result.SetSuccess(null, submitResult.ResultMsg);
                        }
                    }
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 品质不合格品管理
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public dynamic LotRejectTxn(LotRejectRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            if (request.JudgmentResult != "4" && string.IsNullOrEmpty(request.DefectReason) && (request.Spec == null || request.Resource == null || string.IsNullOrEmpty(request.Spec.Name) || string.IsNullOrEmpty(request.Resource.Name)))
            {
                submitResult.ResultMsg = "不良项,工序,设备不能为空";
                return result.SetFail(null, submitResult.ResultMsg);
            }

            if (request.JudgmentResult == "2" && string.IsNullOrEmpty(request.ReworkWorkflow))
            {
                submitResult.ResultMsg = "返工处理必须填写返工流程和不良项";
                return result.SetFail(null, submitResult.ResultMsg);
            }
            if (request.JudgmentResult == "3" && string.IsNullOrEmpty(request.ScrapQty))
            {
                submitResult.ResultMsg = "报废处理必须填写报废数量和报废原因";
                return result.SetFail(null, submitResult.ResultMsg);
            }

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "YP_LotRejectTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("ContainerName").SetValue(request.ContainerName);
                _inputData.DataField("HistoryId").SetValue(request.HistoryId);//异常登记ID
                _inputData.DataField("CreateTime").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                _inputData.DataField("CreateUser").SetValue(request.CreateUser);
                _inputData.NamedObjectField("Employee").SetRef(request.EmployeeName);
                _inputData.DataField("HandlerStatus").SetValue(request.HandlerStatus);
                _inputData.DataField("JudgmentResult").SetValue(request.JudgmentResult);
                _inputData.DataField("DeptName").SetValue(request.DeptName);
                _inputData.DataField("ReworkWorkflow").SetValue(request.ReworkWorkflow);
                _inputData.DataField("ScrapQty").SetValue(request.ScrapQty);
                _inputData.DataField("DefectReason").SetValue(request.DefectReason);
                _inputData.DataField("Remark").SetValue(request.Remark);
                if (request.JudgmentResult != "4")
                {
                    _inputData.RevisionedObjectField("YP_Spec").SetRef(request.Spec.Name, request.Spec.Version, false);
                    _inputData.NamedObjectField("YP_Resource").SetRef(request.Resource.Name);
                }


                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    var re = ExcuteExceptionSend(request);
                    if (re.Result == 0)
                    {
                        result.SetFail(null, re.Message);
                    }
                    else
                    {
                        result.SetSuccess(null, re.Message);
                    }
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }
        /// <summary>
        /// 抽检检验单服务
        /// </summary>
        /// <param name="IQC"></param>
        /// <returns></returns>
        public dynamic IQCInspectionCreateTxn(IQCInspectionRequest IQC)
        {
            SubmitResult submitResult = new SubmitResult();
            QueryService query = new QueryService();
            BaseResult<string> result = new BaseResult<string>();

            #region add by zt 2024-12-24
            if (IQC.YP_InputType == "5")
            {
                //增加后台校验检验单状态

                dynamic StatusList = query.GetIQCStatusInfo(IQC.YP_IQCInspectionTask);
                if (StatusList.Data.IQCStatus != "2")
                {
                    result.SetFail(null, "检验单状态不为“检验中”，不可检验！");
                    return result;
                }
            }
            else if (IQC.YP_InputType == "3")
            {
                dynamic StatusList = query.GetIQCStatusInfo(IQC.YP_IQCInspectionTask);
                if (StatusList.Data.IQCStatus != "1")
                {
                    result.SetFail(null, "检验单状态不为“待接收”，不可检验！");
                    return result;
                }
            }

            #endregion

            try
            {
                string serviceName = "YP_IQCInspectionTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.NamedObjectField("YP_Container").SetRef(IQC.YP_Container);
                //_inputData.DataField("YP_CreateTime").SetFormattedValue(IQC.YP_CreateTime, DataFormats.FormatDate);
                _inputData.NamedObjectField("YP_Creator").SetRef(IQC.YP_Creator);
                _inputData.DataField("YP_DrawingNo").SetValue(IQC.YP_DrawingNo);
                _inputData.DataField("YP_DrawingVersion").SetValue(IQC.YP_DrawingVersion);
                _inputData.NamedObjectField("YP_Factory").SetRef(IQC.YP_Factory);
                _inputData.DataField("YP_File").SetValue(IQC.YP_File);
                _inputData.DataField("YP_InputType").SetValue(IQC.YP_InputType);
                _inputData.DataField("YP_InspectionRemarks").SetValue(IQC.YP_InspectionRemarks);
                if (IQC.YP_InspectionResults == "2")
                {
                    _inputData.DataField("YP_InspectionResults").SetValue("不合格");
                }
                if (IQC.YP_InspectionResults == "1")
                {
                    _inputData.DataField("YP_InspectionResults").SetValue("合格");
                }
                //  _inputData.DataField("YP_InspectionTime").SetFormattedValue(IQC.YP_InspectionTime, DataFormats.FormatDate);
                _inputData.NamedObjectField("YP_Inspector").SetRef(IQC.YP_Inspector);
                var details = _inputData.SubentityList("YP_IQCCenters");
                if (IQC.YP_IQCCenter != null && IQC.YP_IQCCenter.Count > 0)
                {
                    foreach (var item in IQC.YP_IQCCenter)
                    {
                        var thing = details.AppendItem();
                        thing.NamedObjectField("YP_IQCCenter").SetRef(item);
                        //thing.DataField("Name").SetValue(item);
                    }
                }

                //_inputData.NamedObjectField("YP_IQCCenter").SetRef(IQC.YP_IQCCenter);
                _inputData.NamedObjectField("YP_IQCInspectionTask").SetRef(IQC.YP_IQCInspectionTask);
                _inputData.DataField("YP_IQCInspectionTaskName").SetValue(IQC.YP_IQCInspectionTaskName);
                _inputData.DataField("YP_IQCStatus").SetValue(IQC.YP_IQCStatus);
                _inputData.NamedObjectField("YP_MfgOrder").SetRef(IQC.YP_MfgOrder);
                _inputData.NamedObjectField("YP_NumberingRule").SetRef(IQC.YP_NumberingRule);
                _inputData.RevisionedObjectField("YP_Product").SetRef(IQC.YP_Product, "1", false);
                _inputData.DataField("YP_ProductDescription").SetValue(IQC.YP_ProductDescription);
                //  _inputData.DataField("YP_ReceivingTime").SetFormattedValue(IQC.YP_ReceivingTime, DataFormats.FormatDate);
                _inputData.NamedObjectField("YP_Recipient").SetRef(IQC.YP_Recipient);
                _inputData.NamedObjectField("YP_Resource").SetRef(IQC.YP_Resource);
                _inputData.DataField("YP_Shift").SetValue(IQC.YP_Shift);
                _inputData.RevisionedObjectField("YP_Spec").SetRef(IQC.YP_Spec, "1", false);
                _inputData.NamedObjectField("YP_WorkCenter").SetRef(IQC.YP_WorkCenter);
                oService.SetExecute();
                ICsiRequestData requestData;

                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 生产设备登记
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public dynamic RecordResourceTxn(RecordResourceRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "YP_RecordResourceTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                if (!string.IsNullOrEmpty(request.CDOID))
                    _inputData.DataField("YP_RecordResourceId").SetValue(request.CDOID);
                _inputData.NamedObjectField("YP_Container").SetRef(request.ContainerName);
                _inputData.RevisionedObjectField("YP_Product").SetRef(request.ProductName, "", true);
                _inputData.RevisionedObjectField("YP_Spec").SetRef(request.SpecName, "", true);
                _inputData.NamedObjectField("YP_Employee").SetRef(request.EmployeeName);
                _inputData.DataField("YP_CreateTime").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                if (request.RecordResourceDe?.Count > 0)
                {
                    ICsiSubentityList outServiceDetails = _inputData.SubentityList("YP_RecordResourceDetails");
                    foreach (var item in request.RecordResourceDe)
                    {
                        ICsiSubentity listItem = outServiceDetails.AppendItem();
                        listItem.NamedObjectField("YP_Resource").SetRef(item.ResourceName);
                        if (item.Qty != null)
                            listItem.DataField("YP_Qty").SetValue(item.Qty.ToString());
                        if (item.Qty != null)
                            listItem.DataField("YP_ProStartTime").SetFormattedValue(item.ProStartTime.ToString(), DataFormats.FormatDate);
                        if (item.Qty != null)
                            listItem.DataField("YP_ProEndTime").SetFormattedValue(item.ProEndTime.ToString(), DataFormats.FormatDate);
                    }
                }

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic PrintContainerLabel(PrintLabelRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "PrintContainerLabel";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                inputData.DataField("LabelCount").SetValue(request.Qty);
                inputData.NamedObjectField("Container").SetRef(request.Container);
                inputData.NamedObjectField("TaskContainer").SetRef(request.Container);
                inputData.NamedObjectField("PrintQueue").SetRef(request.Printer);
                inputData.RevisionedObjectField("PrinterLabelDefinition").SetRef(request.PrintLabel, "", true);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic ReprintContainerLabel(PrintLabelRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "ReprintContainerLabel";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                inputData.DataField("LabelCount").SetValue("1");
                inputData.NamedObjectField("Container").SetRef(request.Container);
                inputData.NamedObjectField("TaskContainer").SetRef(request.Container);
                inputData.NamedObjectField("PrintQueue").SetRef(request.Printer);
                inputData.ObjectField("LabelHistorySummary").SetObjectId(request.LabelHistorySummaryId);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 设备&&模具绑定
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public dynamic ResourceToolTxn(ResourceToolRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            string sqlStr = string.Empty;
            try
            {
                sqlStr = $@"select re.resourcename ResourceName,tl.resourcename ToolName from resourcedef re
                                    left join (select re.parentresourceid,cd.cdoname,re.resourcename from resourcedef re 
                                    inner join cdodefinition cd on cd.cdodefid = re.cdotypeid
                                    left join w_productbytooldetails pt on pt.toolid = re.resourceid) tl on tl.parentresourceid = re.resourceid and tl.cdoname = 'Tool'
                                    where 1=1
                                    and re.resourcename = :ResourceName";
                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("ResourceName", request.Resource);
                dynamic resource = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, parameters);
                if (string.IsNullOrEmpty(request.Resource))
                {
                    return result.SetFail(null,"设备不能为空！");
                }
                switch (request.Action)
                {
                    case "Bind":
                        if (resource != null && !string.IsNullOrEmpty(resource.TOOLNAME))
                        {
                            result.SetFail(null, $@"设备：{resource.RESOURCENAME}已经绑定有模具{resource.TOOLNAME}");
                            return result;
                        }
                        break;
                    case "UnBind":
                        if (request.Tool.Name !=resource.TOOLNAME)
                        {
                            result.SetFail(null, $@"设备：{resource.RESOURCENAME}绑定的模具是{resource.TOOLNAME}，不能与{request.Tool.Name}解绑");
                            return result;
                        }
                        break;
                }
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "ResourceSetupTransition";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                inputData.DataField("Availability").SetValue(request.Availability);
                inputData.DataField("Comments").SetValue(request.Comments);
                inputData.DataField("ES_UpdateAll").SetValue(request.ES_UpdateAll?.ToString());
                inputData.NamedObjectField("ResourceStatusCode").SetRef(request.ResourceStatusCode);
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                if (request.Tool!= null && !string.IsNullOrEmpty(request.Tool.Name)&&request.Action =="Bind")
                {
                   var tools = inputData.SubentityList("Tools");
                   var item = tools.AppendItem();
                    item.SetAttribute("__listItemAction", request.Tool.Action);
                    item.DataField("__name").SetValue(request.Tool.Name);
                }
                inputData.DataField("UpdateTools").SetValue(request.UpdateTools?.ToString());
      
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic Login(BaseRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService("ServiceDoc", "Service");

                ICsiObject inputData;
                var oService = _camstarXmlClient.GetService();
                inputData = oService.InputData();

                inputData.NamedObjectField("User").SetRef(request.User);
                inputData.NamedObjectField("Employee").SetRef(request.User);
                inputData.DataField("Password").SetValue(request.Password);

                oService.Perform("VerifyUser");

                // Execute the service
                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp("ServiceDoc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic SetResourceStatus(ResourceRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService("ResourceSetupTransitionDoc", "ResourceSetupTransition");

                ICsiObject inputData;
                var oService = _camstarXmlClient.GetService();
                inputData = oService.InputData();

                string status = string.Empty;
                switch (request.Status)
                {
                    case "1":
                        status = "RUN";
                        break;
                    case "2":
                        status = "DOWN";
                        break;
                    case "3":
                        status = "WARING";
                        break;
                    case "4":
                        status = "IDLE";
                        break;
                    default:
                        break;
                }
                inputData.NamedObjectField("ResourceStatusCode").SetRef(status);
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                inputData.DataField("ES_UpdateAll").SetValue("false");
                inputData.DataField("UpdateTools").SetValue("false");

                // Execute the service
                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp("ResourceSetupTransitionDoc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public SubmitResult MaintenanceMesTool(MaintenanceResource resource)
        {
            SubmitResult result = new SubmitResult();
            string actionName = string.Empty;
            string ID = string.Empty;
            string sqlStr = string.Empty;
            try
            {
                
                    _camstarXmlClient = new CamstarXmlClient(resource.User, resource.Password.Trim().DecryptDES(AppSetting.Secret.User));
                    string serviceName = "ToolMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputData2 = oService.InputData();
                if (resource.EventName == "delete")
                {
                    inputData2.NamedObjectField("ObjectToChange").SetRef(resource.ResourceName);
                    oService.Perform("Delete");
                }
                else
                {
                    if (resource.NewResourceName == null || resource.NewResourceName == "")
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "设备编号不能为空";
                        result.ResultData = "";
                        return result;
                    }

                    sqlStr = $@"
                            SELECT pb.PRODUCTNAME FROM PRODUCT p 
                            inner join PRODUCTBASE pb on p.PRODUCTBASEID = pb.PRODUCTBASEID
                            where pb.PRODUCTNAME = :productNo
                            ";
                    DynamicParameters parameter = new DynamicParameters();
                    parameter.Add(":productNo", resource.ToolProductDetails.ProductNo);
                    var produt = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, parameter);
                    if (produt == null)
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "产品找不到";
                        result.ResultData = "";
                        return result;
                    }
                    //if (!string.IsNullOrEmpty(resource.Password))
                    //{
                    //    _camstarXmlClient = new CamstarXmlClient(resource.User, resource.Password.Trim().DecryptDES(AppSetting.Secret.User));
                    //}
                    //string serviceName = "ToolMaint";
                    //_camstarXmlClient.InitializeSession();
                    //_camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    //ICsiService oService = _camstarXmlClient.GetService();
                    //ICsiObject inputData2 = oService.InputData();

                    switch (resource.EventName)
                    {
                        case "load":
                            inputData2.NamedObjectField("ObjectToChange").SetRef(resource.ResourceName);
                            oService.Perform("Load");
                            sqlStr = $@"
                            select wp.w_productbytooldetailsid Indexs,wp.w_productbytooldetailsname from W_ProductByToolDetails wp
                            inner join resourcedef re on re.resourceid = wp.toolid
                            where wp.w_Productno = :productNo
                            and re.resourcename = :toolName
                            ";
                            DynamicParameters parameters = new DynamicParameters();
                            parameters.Add(":productNo", resource.ToolProductDetails.ProductNo);
                            parameters.Add(":toolName", resource.ResourceName);
                            var resourceList = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, parameters);
                            if (resourceList != null)
                            {
                                actionName = "change";
                                ID = resourceList.INDEXS;
                            }
                            else
                            {
                                actionName = "add";
                            }
                            break;
                        case "add":

                            sqlStr = $@"
                                select re.resourcename from resourcedef re 
                                where re.resourcename = :toolName";
                            DynamicParameters ps = new DynamicParameters();
                            ps.Add(":toolName", resource.NewResourceName);
                            var re = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, ps);
                            if (re != null)
                            {
                                inputData2.NamedObjectField("ObjectToChange").SetRef(resource.NewResourceName);
                                oService.Perform("Load");
                            }
                            else
                            {
                                oService.Perform("New");
                            }
                            sqlStr = $@"
                            select wp.w_productbytooldetailsid Indexs,wp.w_productbytooldetailsname from W_ProductByToolDetails wp
                            inner join resourcedef re on re.resourceid = wp.toolid
                            where wp.w_Productno = :productNo
                            and re.resourcename = :toolName";
                            ps = new DynamicParameters();
                            ps.Add(":productNo", resource.ToolProductDetails.ProductNo);
                            ps.Add(":toolName", resource.NewResourceName);
                            re = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, ps);
                            if (re != null)
                            {
                                result.ResulTest = "fail";
                                result.ResultCode = "0";
                                result.ResultMsg = "该产品已存在";
                                result.ResultData = "";
                                return result;
                            }
                            else
                            {
                                actionName = "add";
                            }

                            break;
                        case "delete":
                            inputData2.NamedObjectField("ObjectToChange").SetRef(resource.ResourceName);
                            oService.Perform("Delete");
                            break;
                    }

                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Name").SetValue(resource.NewResourceName);
                    objectChanges.DataField("Description").SetValue(resource.ResourceDescription);
                    objectChanges.NamedObjectField("ResourceFamily").SetRef(resource.EquipmentFamily);
                    objectChanges.DataField("W_MoldType").SetValue(resource.ModelType);
                    if (!string.IsNullOrEmpty(resource.ModelDate))
                    {
                        objectChanges.DataField("W_ModelDate").SetFormattedValue(Convert.ToDateTime(resource.ModelDate).ToString("yyyy-MM-dd HH:mm"), DataFormats.FormatDateAndTime);
                    }

                    if (resource.ToolProductDetails != null)
                    {
                        ICsiSubentityList outServiceDetails = objectChanges.SubentityList("W_ProductByToolDetails");
                        ICsiSubentity listItem = outServiceDetails.AppendItem();
                        listItem.SetAttribute("__listItemAction", actionName);
                        if (actionName == "change")
                        {
                            listItem.DataField("__index").SetValue(string.IsNullOrEmpty(resource.ToolProductDetails.Indexs) ? "0" : resource.ToolProductDetails.Indexs);
                            //listItem.DataField("__key").SetValue(ID);
                            //listItem.SetObjectId(ID);
                        }
                        listItem.DataField("W_ProductNo").SetValue(resource.ToolProductDetails.ProductNo);
                        listItem.DataField("W_ProductDesc").SetValue(resource.ToolProductDetails.ProductDesc);
                        listItem.DataField("W_MaterialQuantity").SetValue(resource.ToolProductDetails.MaterialQuantity);
                        listItem.DataField("W_MaterialThickness").SetValue(resource.ToolProductDetails.MaterialThickness);
                        listItem.DataField("W_MaterialWidth").SetValue(resource.ToolProductDetails.MaterialWidth);
                        listItem.DataField("W_MachineTonnage").SetValue(resource.ToolProductDetails.MachineTonnage);
                        listItem.DataField("W_OutQty").SetValue(resource.ToolProductDetails.OutQty);
                        listItem.DataField("W_PlasticMaterial").SetValue(resource.ToolProductDetails.PlasticMaterial);
                        listItem.DataField("W_RushNumber").SetValue(resource.ToolProductDetails.RushNumber);
                        listItem.DataField("W_SingleWeight").SetValue(resource.ToolProductDetails.SingleWeight);
                        listItem.DataField("W_WaterWeight").SetValue(resource.ToolProductDetails.WaterWeight);
                        listItem.DataField("W_IngredientRatio").SetValue(resource.ToolProductDetails.IngredientRatio);
                        listItem.DataField("W_Frequency").SetValue(resource.ToolProductDetails.Frequency);
                        listItem.DataField("W_ProductionCycle").SetValue(resource.ToolProductDetails.ProductionCycle);
                        listItem.DataField("W_MaterialQuality").SetValue(resource.ToolProductDetails.MaterialQuality);
                        listItem.DataField("W_MoldType").SetValue(resource.ToolProductDetails.ModelType);
                    }
                }
                
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult MaintenanceMesPart(MaintenancePart resource)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                if (!string.IsNullOrEmpty(resource.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(resource.User, resource.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "PartTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputData = oService.InputData();
                inputData.DataField("PartName").SetValue(resource.PartName);
                inputData.DataField("NewPartName").SetValue(resource.NewPartName);
                inputData.RevisionedObjectField("Product").SetRef(resource.ResourceMaterialPart, resource.ResourceMaterialPartRevision, false);
                inputData.DataField("PartQty").SetValue(resource.PartQty.ToString());
                inputData.NamedObjectField("ResourceFamily").SetRef(resource.PartFamily);
                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult MesResourceMaintenance(MaintenanceResource resource)
        {
            SubmitResult result = new SubmitResult();
            if (resource != null)
            {
                try
                {

                    if (!string.IsNullOrEmpty(resource.Password))
                    {
                        _camstarXmlClient = new CamstarXmlClient(resource.User, resource.Password.Trim().DecryptDES(AppSetting.Secret.User));
                    }
                    string serviceName = string.Empty;
                    serviceName = "ResourceMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputData2 = oService.InputData();
                    switch (resource.EventName)
                    {
                        case "load":
                            inputData2.NamedObjectField("ObjectToChange").SetRef(resource.ResourceName);
                            oService.Perform("Load");
                            break;
                        case "add":
                            oService.Perform("New");
                            break;
                        case "delete":
                            inputData2.NamedObjectField("ObjectToChange").SetRef(resource.ResourceName);
                            oService.Perform("Delete");
                            break;
                    }
                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Name").SetValue(resource.NewResourceName);
                    objectChanges.DataField("Description").SetValue(resource.ResourceDescription);
                    objectChanges.NamedObjectField("Factory").SetRef(resource.Factory);
                    objectChanges.NamedObjectField("ResourceFamily").SetRef(resource.EquipmentFamily);

                    objectChanges.DataField("W_Model").SetValue(resource.Model);
                    objectChanges.DataField("W_AssetNumber").SetValue(resource.AssetNumber);
                    objectChanges.DataField("W_Comment").SetValue(resource.Comments);
                    objectChanges.DataField("W_DesignCapacity").SetValue(resource.DesignCapacity);
                    objectChanges.DataField("W_Developmenttime").SetFormattedValue(Convert.ToDateTime(resource.Developmenttime).ToString("yyyy-MM-dd HH:mm"), DataFormats.FormatDateAndTime);
                    objectChanges.DataField("W_EquipmentFunction").SetValue(resource.EquipmentFunction);
                    objectChanges.DataField("W_KeyProcess").SetValue(resource.KeyProcess.ToString());
                    objectChanges.DataField("W_MachineParts").SetValue(resource.MachineParts);
                    objectChanges.DataField("W_PartNumber").SetValue(resource.PartNumber);
                    objectChanges.DataField("W_ProducType").SetValue(resource.ProducType);
                    objectChanges.DataField("W_Specification").SetValue(resource.Specification);
                    objectChanges.DataField("W_Storagelocation").SetValue(resource.Storagelocation);
                    objectChanges.DataField("W_UsageStatus").SetValue(resource.UsageStatus);
                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult ResourceFamilyAdd(ResourceFamily resourceFamily)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                string serviceName = "";
                string pattern = "";
                string partern_calture = "";
                switch (resourceFamily.FamilyType)
                {
                    case "设备":
                        serviceName = "ResourceFamilyMaint";
                        // 正则: 长度为9，数字1开头，后面可以是字母或数字
                        pattern = @"^1[A-Za-z0-9]{8}$";
                        partern_calture = "Length:9;start with:1";
                        break;
                    case "治工具":
                        serviceName = "ToolFamilyMaint";
                        // 正则: 长度为7，数字2或3开头，后面可以是字母或数字
                        pattern = @"^[23][A-Za-z0-9]{6}$";
                        partern_calture = "Length:7;start with:2 or 3";
                        break;
                    case "配件":
                        serviceName = "ToolFamilyMaint";
                        // 正则: 长度为7，数字3开头，后面可以是字母或数字
                        pattern = @"^3[A-Za-z0-9]{6}$";
                        partern_calture = "Length:7;start with:3";
                        break;
                }
                string splice_string = "ResourceFamilyName must match the pattern:";
                if (!Regex.IsMatch(resourceFamily.ResourceFamilyName, pattern))
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = splice_string + $" {partern_calture}";
                    result.ResultData = "";
                    return result;
                }

                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                oService.Perform("New");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(resourceFamily.ResourceFamilyName);
                objectChanges.DataField("Description").SetValue(resourceFamily.Description);
                objectChanges.RevisionedObjectField("YP_EquipDailyCheck").SetRef(resourceFamily.Biz_EquipDailyCheck, "1", false);
                objectChanges.RevisionedObjectField("YP_InProcessCheck").SetRef(resourceFamily.Biz_InProcessCheck, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipMaintL1").SetRef(resourceFamily.Biz_EquipMaintL1, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipMaintL2").SetRef(resourceFamily.Biz_EquipMaintL2, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipMaintL3").SetRef(resourceFamily.Biz_EquipMaintL3, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipHealthCheck").SetRef(resourceFamily.Biz_EquipHealthCheck, "1", false);
                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult ResourceFamilyEdit(ResourceFamily resourceFamily)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                string serviceName = "";
                string pattern = "";
                string partern_calture = "";
                switch (resourceFamily.FamilyType)
                {
                    case "设备":
                        serviceName = "ResourceFamilyMaint";
                        // 正则: 长度为9，数字1开头，后面可以是字母或数字
                        pattern = @"^1[A-Za-z0-9]{8}$";
                        partern_calture = "Length:9;start with:1";
                        break;
                    case "治工具":
                        serviceName = "ToolFamilyMaint";
                        // 正则: 长度为7，数字2或3开头，后面可以是字母或数字
                        pattern = @"^[23][A-Za-z0-9]{6}$";
                        partern_calture = "Length:7;start with:2 or 3";
                        break;
                    case "配件":
                        serviceName = "ToolFamilyMaint";
                        // 正则: 长度为7，数字3开头，后面可以是字母或数字
                        pattern = @"^3[A-Za-z0-9]{6}$";
                        partern_calture = "Length:7;start with:3";
                        break;
                }
                string splice_string = "ResourceFamilyName must match the pattern:";
                if (!Regex.IsMatch(resourceFamily.NewResourceFamilyName, pattern))
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = splice_string + $"{partern_calture}";
                    result.ResultData = "";
                    return result;
                }

                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData2 = oService.InputData();
                inputData2.NamedObjectField("ObjectToChange").SetRef(resourceFamily.ResourceFamilyName);

                oService.Perform("Load");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(resourceFamily.NewResourceFamilyName);
                objectChanges.DataField("Description").SetValue(resourceFamily.Description);
                objectChanges.RevisionedObjectField("YP_EquipDailyCheck").SetRef(resourceFamily.Biz_EquipDailyCheck, "1", false);
                objectChanges.RevisionedObjectField("YP_InProcessCheck").SetRef(resourceFamily.Biz_InProcessCheck, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipMaintL1").SetRef(resourceFamily.Biz_EquipMaintL1, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipMaintL2").SetRef(resourceFamily.Biz_EquipMaintL2, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipMaintL3").SetRef(resourceFamily.Biz_EquipMaintL3, "1", false);
                objectChanges.RevisionedObjectField("YP_EquipHealthCheck").SetRef(resourceFamily.Biz_EquipHealthCheck, "1", false);

                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult ResourceFamilyDelete(ResourceFamily resourceFamily)
        {
            SubmitResult result = new SubmitResult();
            if (resourceFamily != null)
            {
                try
                {
                    string serviceName = "";
                    switch (resourceFamily.FamilyType)
                    {
                        case "设备":
                            serviceName = "ResourceFamilyMaint";
                            break;
                        case "治工具":
                            serviceName = "ToolFamilyMaint";
                            break;
                        case "配件":
                            serviceName = "ToolFamilyMaint";
                            break;
                    }
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);

                    ICsiObject inputData;
                    var oService = _camstarXmlClient.GetService();
                    inputData = oService.InputData();
                    inputData.NamedObjectField("ObjectToChange").SetRef(resourceFamily.ResourceFamilyName);
                    var per = oService.Perform("Delete");

                    // Execute the service
                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    if (oResult.ResultMsg.Contains("Error 679484855"))
                    {
                        oResult.ResultMsg = "Resource Family has been referenced and cannot be deleted!";
                    }
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
                return result;
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult AddResourceMaterialPart(ResourceMaterialPart resourceMaterialPart)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                string serviceName = "ResourceMaterialPartMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                oService.Perform("New");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(resourceMaterialPart.ResourceMaterialPart_Name);
                objectChanges.DataField("Revision").SetValue(resourceMaterialPart.ResourceMaterial_Revision);
                objectChanges.DataField("Description").SetValue(resourceMaterialPart.ResourceMaterial_Description);
                //objectChanges.NamedObjectField("PartType").SetRef(resourceMaterialPart.ResourceMaterial_ProductType);
                objectChanges.DataField("MinQtyReorderLimit").SetValue(resourceMaterialPart.ResourceMaterial_MinQtyReorderLimit.ToString());
                objectChanges.NamedObjectField("MinQtyReorderEmailGroup").SetRef(resourceMaterialPart.ResourceMaterial_MinQtyReorderEmailGroup);
                objectChanges.DataField("PartType").SetValue(resourceMaterialPart.ResourceMaterial_ProductType);
                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult EditResourceMaterialPart(ResourceMaterialPart resourceMaterialPart)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                string serviceName = "ResourceMaterialPartMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData2 = oService.InputData();
                inputData2.RevisionedObjectField("ObjectToChange").SetRef(resourceMaterialPart.ResourceMaterialPart_Name, resourceMaterialPart.ResourceMaterial_Revision, false);

                oService.Perform("Load");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(resourceMaterialPart.NewResourceMaterialPart_Name);
                objectChanges.DataField("Revision").SetValue(resourceMaterialPart.ResourceMaterial_Revision);
                objectChanges.DataField("Description").SetValue(resourceMaterialPart.ResourceMaterial_Description);
                //objectChanges.NamedObjectField("ProductType").SetRef(resourceMaterialPart.ResourceMaterial_ProductType);
                objectChanges.DataField("MinQtyReorderLimit").SetValue(resourceMaterialPart.ResourceMaterial_MinQtyReorderLimit.ToString());
                objectChanges.NamedObjectField("MinQtyReorderEmailGroup").SetRef(resourceMaterialPart.ResourceMaterial_MinQtyReorderEmailGroup);
                objectChanges.DataField("PartType").SetValue(resourceMaterialPart.ResourceMaterial_ProductType);

                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult ResourceMaterialPartDelete(ResourceMaterialPart resourceMaterialPart)
        {
            SubmitResult result = new SubmitResult();
            if (resourceMaterialPart != null)
            {
                try
                {
                    string serviceName = "ResourceMaterialPartMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);

                    ICsiObject inputData;
                    var oService = _camstarXmlClient.GetService();
                    inputData = oService.InputData();
                    inputData.RevisionedObjectField("ObjectToChange").SetRef(resourceMaterialPart.ResourceMaterialPart_Name, resourceMaterialPart.ResourceMaterial_Revision, false);
                    var per = oService.Perform("DeleteAllRevisions");

                    // Execute the service
                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    if (oResult.ResultMsg.Contains("Error 679484855"))
                    {
                        oResult.ResultMsg = "Resource Material Part has been referenced and cannot be deleted!";
                    }
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
                return result;
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult RegistMesTool(Tool tool)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                if (tool.Biz_LifetimeWarning > tool.Biz_LifetimeLimit)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = "LifetimeWarning Cannot be greater than LifetimeLimit!";
                    result.ResultData = "";
                    return result;
                }
                string serviceName = "ToolMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiObject inputData;
                var oService = _camstarXmlClient.GetService();
                oService.Perform("New");

                inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                //string toolFamily = tool.ToolFamily;
                //if (tool.ToolFamily.Length < 7)
                //{
                //    int length = 7 - tool.ToolFamily.Length;
                //    for (int i = 0; i < length; i++)
                //    {
                //        toolFamily += "0";
                //    }
                //}
                //else if (tool.ToolFamily.Length > 7)
                //{
                //    toolFamily = toolFamily.Substring(0, 7);
                //}
                //else
                //{
                //    toolFamily = tool.ToolFamily;
                //}
                //string name = toolFamily;
                //string sql = @"select RESOURCENAME as ResourceName from ResourceDef where RESOURCENAME = :toolName ";
                //DynamicParameters parameters = new DynamicParameters();
                //parameters.Add(":toolName", name);
                //List<Resource> list = new List<Resource>();
                //list = DBServerProvider.SqlDapper.QueryList<Resource>(sql, parameters);
                //if (list.Count > 0)
                //{
                //    do
                //    {
                //        name = toolFamily;
                //        parameters = new DynamicParameters();
                //        parameters.Add(":toolName", name);
                //        list = DBServerProvider.SqlDapper.QueryList<Resource>(sql, parameters);
                //    } while (list.Count > 0);
                //}
                objectChanges.DataField("Name").SetValue(tool.Name);
                objectChanges.DataField("Description").SetValue(tool.Description);
                objectChanges.NamedObjectField("Factory").SetRef("1200");
                //objectChanges.DataField("Biz_AssetPic").SetValue(tool.Biz_AssetPic);
                objectChanges.NamedObjectField("ResourceFamily").SetRef(tool.ToolFamily);
                //objectChanges.NamedObjectField("Biz_PhysicalLocation").SetRef(tool.Biz_PhysicalLocation);
                //objectChanges.NamedObjectField("Biz_PhysicalPosition").SetRef(tool.Biz_PhysicalPosition);
                //objectChanges.DataField("Biz_AssetDept").SetValue(tool.Biz_AssetDept);
                //objectChanges.DataField("Biz_AssetOwner1").SetValue(tool.Biz_AssetOwner1);
                //objectChanges.DataField("Biz_AssetVendor").SetValue(tool.Biz_AssetVendor);
                objectChanges.DataField("VendorModel").SetValue(tool.VendorModel);
                //objectChanges.DataField("VendorSerialNumber").SetValue(tool.VendorSerialNumber);;
                //objectChanges.DataField("Biz_LifetimeLimit").SetValue(tool.Biz_LifetimeLimit.ToString());
                objectChanges.NamedObjectField("EmailGroup").SetRef(tool.EmailGroup);
                //objectChanges.DataField("Biz_Usage").SetValue("0");
                //ICsiSubentityList subentityList = objectChanges.SubentityList("Biz_EmailList");
                //foreach (Biz_EmailList child in tool.Biz_EmailList)
                //{
                //    ICsiSubentity subentity = subentityList.AppendItem();
                //    subentity.DataField("Name").SetValue(name);
                //    subentity.DataField("Biz_EmailUrl").SetValue(child.Biz_EmailUrl);
                //    subentity.DataField("Biz_Remark").SetValue(child.Biz_Remark);
                //}
                //ICsiSubentityList subentityList1 = objectChanges.SubentityList("Biz_PNList");
                //foreach (Biz_PNList child in tool.Biz_PNList)
                //{
                //    ICsiSubentity subentity = subentityList1.AppendItem();
                //    subentity.DataField("Name").SetValue(name);
                //    subentity.RevisionedObjectField("Product").SetRef(child.ProductName, child.ProductRevision, false);
                //}
                // Execute the service
                oService.SetExecute();
                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult AddRequirement(MaintenanceReqRequest request)
        {
            SubmitResult result = new SubmitResult();
            if (request != null)
            {
                try
                {
                    string serviceName = "";
                    switch (request.RequirementType)
                    {
                        case "重复周期":
                            serviceName = "RecurringDateReqMaint";
                            break;
                        case "使用寿命":
                            serviceName = "ThruputReqMaint";
                            break;
                        default:
                            serviceName = "DateReqMaint";
                            break;
                    }
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputDataSync = oService.InputData();
                    inputDataSync.DataField("SyncName").SetValue(request.RequirementName);
                    inputDataSync.DataField("SyncRevision").SetValue(request.RequirementRevision);
                    inputDataSync.Perform("Sync");

                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    //名称
                    objectChanges.DataField("Name").SetValue(request.NewRequirementName);
                    //版本
                    objectChanges.DataField("Revision").SetValue(request.RequirementRevision);
                    //描述
                    objectChanges.DataField("Description").SetValue(request.RequirementDescription);
                    //维护原因
                    objectChanges.NamedObjectField("MaintenanceReason").SetRef(request.MaintenanceReason);
                    var listdetails = objectChanges.SubentityList("Checklist");
                    foreach (var detail in request.CheckListList)
                    {
                        string actionName;
                        if (!string.IsNullOrEmpty(detail.Action))
                        {
                            actionName = detail.Action;
                        }
                        else
                        {
                            actionName = "change";
                        }
                        var itemInfo = listdetails.AppendItem();
                        itemInfo.SetAttribute("__listItemAction", actionName);
                        Type t = detail.GetType();
                        PropertyInfo[] propertyInfos = t.GetProperties();
                        foreach (PropertyInfo item in propertyInfos)
                        {
                            if (item.PropertyType == typeof(string) && item.Name != "Action")
                            {
                                string name = item.Name;
                                object? value = item.GetValue(detail);
                                if (!string.IsNullOrEmpty(value?.ToString()))
                                {
                                    itemInfo.DataField(name).SetValue(value.ToString());
                                }
                            }
                            else if (item.PropertyType.IsEnum)
                            {
                                string name = item.Name;
                                object? value = item.GetValue(detail);
                                if (value != null)
                                {
                                    int i = (int)value;
                                    itemInfo.DataField(name).SetValue(i.ToString());
                                }
                            }
                            else if (item.PropertyType == typeof(bool))
                            {
                                string name = item.Name;
                                object? value = item.GetValue(detail);
                                if (value != null)
                                {
                                    itemInfo.DataField(name).SetValue(value.ToString());
                                }
                            }
                            else if (item.PropertyType == typeof(ModelingResult))
                            {
                                string name = item.Name;
                                ModelingResult? value = (ModelingResult)item.GetValue(detail);
                                if (value != null && !string.IsNullOrEmpty(value.Name) && value.Version == null)
                                {
                                    itemInfo.NamedObjectField(name).SetRef(value.Name.ToString());
                                }
                                else if (value != null && !string.IsNullOrEmpty(value.Name) && value.Version != null)
                                {
                                    itemInfo.RevisionedObjectField(name).SetRef(value.Name.ToString(), value.Version.ToString(), false);
                                }
                                else
                                {
                                    itemInfo.NamedObjectField(name).SetRef(null);
                                }
                            }
                        }
                    }
                    switch (request.RequirementType)
                    {
                        case "重复周期":
                            //重复方式
                            objectChanges.DataField("RecurringDatePattern").SetValue(request.RecurringDatePattern);
                            objectChanges.DataField("DayOfWeek").SetValue(request.DayOfWeek);
                            objectChanges.DataField("DayOfMonth").SetValue(request.DayOfMonth);
                            objectChanges.DataField("DayOfMonth").SetValue(request.DayOfMonth);
                            objectChanges.DataField("MonthOfYear").SetValue(request.MonthOfYear);
                            //switch (request.RecurringDatePattern)
                            //{
                            //    case "2":
                            //        //重复方式选择周保养时
                            //        objectChanges.DataField("DayOfWeek").SetValue(request.DayOfWeek);
                            //        break;
                            //    case "3":
                            //        //重复方式选择月保养时
                            //        objectChanges.DataField("DayOfMonth").SetValue(request.DayOfMonth);
                            //        break;
                            //    case "4":
                            //        //重复方式选择年保养时
                            //        objectChanges.DataField("DayOfMonth").SetValue(request.DayOfMonth);
                            //        objectChanges.DataField("MonthOfYear").SetValue(request.MonthOfYear);
                            //        break;
                            //    default:
                            //        objectChanges.DataField("DayOfWeek").SetValue(request.DayOfWeek);
                            //        objectChanges.DataField("DayOfMonth").SetValue(request.DayOfMonth);
                            //        objectChanges.DataField("DayOfMonth").SetValue(request.DayOfMonth);
                            //        objectChanges.DataField("MonthOfYear").SetValue(request.MonthOfYear);
                            //        break;
                            //}
                            //频率
                            objectChanges.DataField("Frequency").SetValue(request.Frequency);
                            //当为重复周期时，需维护开始时间和结束时间和重复次数
                            objectChanges.DataField("SeedDate").SetFormattedValue(Convert.ToDateTime(request.SeedDate).ToString("yyyy-MM-dd HH:mm"), DataFormats.FormatDateAndTime);
                            //重复次数
                            if (!string.IsNullOrWhiteSpace(request.Occurrences))
                            {
                                objectChanges.DataField("Occurrences").SetValue(request.Occurrences);
                            }
                            //结束时间
                            if (!string.IsNullOrWhiteSpace(request.EndDate))
                            {
                                objectChanges.DataField("EndDate").SetFormattedValue(Convert.ToDateTime(request.EndDate).ToString("yyyy-MM-dd HH:mm"), DataFormats.FormatDateAndTime);
                            }
                            //警告余量
                            string warningPeriod = ConvertTimeToDays(request.WarningPeriod).ToString();
                            objectChanges.DataField("WarningPeriod").SetValue(warningPeriod);
                            //禁用容许量
                            string tolerancePeriod = ConvertTimeToDays(request.TolerancePeriod).ToString();
                            objectChanges.DataField("TolerancePeriod").SetValue(tolerancePeriod);
                            break;
                        case "使用寿命":
                            objectChanges.DataField("Qty").SetValue(request.Qty);
                            objectChanges.NamedObjectField("UOM").SetRef(request.UOM);
                            if (!string.IsNullOrWhiteSpace(request.WarningQty))
                            {
                                objectChanges.DataField("WarningQty").SetValue(request.WarningQty);
                            }
                            if (!string.IsNullOrWhiteSpace(request.ToleranceQty))
                            {
                                objectChanges.DataField("ToleranceQty").SetValue(request.ToleranceQty);
                            }
                            break;
                        default:
                            objectChanges.DataField("ScheduleDate").SetFormattedValue(Convert.ToDateTime(request.ScheduleDate).ToString("yyyy-MM-dd HH:mm"), DataFormats.FormatDateAndTime);
                            //警告余量
                            string warningPeriod2 = ConvertTimeToDays(request.WarningPeriod).ToString();
                            objectChanges.DataField("WarningPeriod").SetValue(warningPeriod2);
                            //禁用容许量
                            string tolerancePeriod2 = ConvertTimeToDays(request.TolerancePeriod).ToString();
                            objectChanges.DataField("TolerancePeriod").SetValue(tolerancePeriod2);
                            break;
                    }
                    //待处理邮件列表
                    DynamicParameters Ps = new DynamicParameters();
                    string sql = @"select nct.notificationtargetname as ""EmailNotificationName""
                                          from maintreqpendingemailtarget mpm
                                          left join NotificationTarget nct
                                            on mpm.pendingemailtargetid = nct.notificationtargetid
                                          left join MaintenanceReq mr
                                            on mpm.maintenancereqid = mr.maintenancereqid
                                          left join maintenancereqbase mb
                                            on mr.maintenancereqbaseid = mb.maintenancereqbaseid
                                         where mb.maintenancereqname = @maintenancereqname and mr.revision = @revision ";
                    Ps.Add("@maintenancereqname", request.RequirementName);
                    Ps.Add("@revision", request.RequirementRevision);
                    var list = DBServerProvider.SqlDapper.QueryList<EmailTarget>(sql, Ps);
                    ICsiNamedObjectList subentityList = objectChanges.NamedObjectList("PendingEmailTarget");
                    for (int i = 0; i < list.Count; i++)
                    {
                        subentityList.DeleteItemByIndex(0);
                    }
                    foreach (EmailTarget child in request.PendingEmailTargetList)
                    {
                        var subentity = subentityList.AppendItem(child.EmailNotificationName);
                    }
                    if (request.PendingEmailTargetList != null)
                    {
                        objectChanges.DataField("PendingEmailText").SetValue(request.PendingEmailText);
                    }

                    //到期邮件列表
                    string sql2 = @"select nct.notificationtargetname as ""EmailNotificationName""
                                          from maintreqdueemailtarget mpm
                                          left join NotificationTarget nct
                                            on mpm.dueemailtargetid = nct.notificationtargetid
                                          left join MaintenanceReq mr
                                            on mpm.maintenancereqid = mr.maintenancereqid
                                          left join maintenancereqbase mb
                                            on mr.maintenancereqbaseid = mb.maintenancereqbaseid
                                         where mb.maintenancereqname = @maintenancereqname  and mr.revision = @revision";
                    var list2 = DBServerProvider.SqlDapper.QueryList<EmailTarget>(sql2, Ps);
                    ICsiNamedObjectList subentityList2 = objectChanges.NamedObjectList("DueEmailTarget");
                    for (int i = 0; i < list2.Count; i++)
                    {
                        subentityList2.DeleteItemByIndex(0);
                    }
                    foreach (EmailTarget child in request.DueEmailTargetList)
                    {
                        var subentity = subentityList2.AppendItem(child.EmailNotificationName);
                    }
                    if (request.DueEmailTargetList != null)
                    {
                        objectChanges.DataField("DueEmailText").SetValue(request.DueEmailText);
                    }

                    //禁用提醒邮件列表
                    string sql3 = @" select nct.notificationtargetname as ""EmailNotificationName""
                                          from maintreqpastdueemailtarget mpdm
                                          left join NotificationTarget nct
                                            on mpdm.pastdueemailtargetid = nct.notificationtargetid
                                          left join MaintenanceReq mr
                                            on mpdm.maintenancereqid = mr.maintenancereqid
                                          left join maintenancereqbase mb
                                            on mr.maintenancereqbaseid = mb.maintenancereqbaseid
                                         where mb.maintenancereqname = @maintenancereqname  and mr.revision = @revision";
                    var list3 = DBServerProvider.SqlDapper.QueryList<EmailTarget>(sql3, Ps);
                    var subentityList3 = objectChanges.NamedObjectList("PastDueEmailTarget");
                    for (int i = 0; i < list3.Count; i++)
                    {
                        subentityList3.DeleteItemByIndex(0);
                    }
                    foreach (EmailTarget child in request.PastDueEmailTargetList)
                    {
                        var subentity = subentityList3.AppendItem(child.EmailNotificationName);
                    }
                    if (request.PastDueEmailTargetList != null)
                    {
                        objectChanges.DataField("PastDueEmailText").SetValue(request.PastDueEmailText);
                    }
                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult DeleteRequirement(DelMaintenanceReqRequest request)
        {
            SubmitResult result = new SubmitResult();
            if (request != null)
            {
                try
                {
                    string serviceName = "";
                    switch (request.RequirementType)
                    {
                        case "重复周期":
                            serviceName = "RecurringDateReqMaint";
                            break;
                        case "使用寿命":
                            serviceName = "ThruputReqMaint";
                            break;
                        default:
                            serviceName = "DateReqMaint";
                            break;
                    }
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);

                    ICsiObject inputData;
                    var oService = _camstarXmlClient.GetService();
                    inputData = oService.InputData();
                    inputData.RevisionedObjectField("ObjectToChange").SetRef(request.RequirementName, request.RequirementRevision, false);
                    var per = oService.Perform("DeleteAllRevisions");

                    // Execute the service
                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    if (oResult.ResultMsg.Contains("Error 679484855"))
                    {
                        oResult.ResultMsg = "Requirement has been referenced and cannot be deleted!";
                    }
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult ResourceActivation(ResourceActivationRequest request)
        {
            SubmitResult result = new SubmitResult();
            if (request != null)
            {
                try
                {
                    string serviceName = "ResourceActivation";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();

                    ICsiObject inputData = oService.InputData();
                    inputData.RevisionedObjectField("MaintenanceReq").SetRef(request.MaintenanceReqName, "1", false);
                    if (!string.IsNullOrWhiteSpace(request.ResourceGroup))
                    {
                        inputData.NamedObjectField("ResourceGroup").SetRef(request.ResourceGroup);
                    }

                    ICsiSubentityList inputDetails = (ICsiSubentityList)inputData.SubentityList("ServiceDetails");
                    foreach (var item in request.ServiceDetails)
                    {
                        ICsiSubentity DetailsItem;
                        DetailsItem = (ICsiSubentity)inputDetails.AppendItem();
                        if (item.Activated == "1")
                        {
                            DetailsItem.DataField("Activated").SetValue("true");
                        }
                        else
                        {
                            DetailsItem.DataField("Activated").SetValue("false");
                        }
                        DetailsItem.DataField("ClassActivated").SetValue("false");
                        if (item.OriginalActivated == "1")
                        {
                            DetailsItem.DataField("OriginalActivated").SetValue("true");
                        }
                        else
                        {
                            DetailsItem.DataField("OriginalActivated").SetValue("false");
                        }
                        var resource = DetailsItem.NamedObjectField("Resource");
                        if (item.ObjectType == "RESOURCE")
                        {
                            resource.SetObjectType("Resource");
                        }
                        if (item.ObjectType == "TOOL")
                        {
                            resource.SetObjectType("Tool");
                        }
                        if (item.ObjectType == "Part")
                        {
                            resource.SetObjectType("Part");
                        }
                        resource.SetObjectId(item.ResourceId);
                        resource.SetRef(item.ResourceName);
                        DetailsItem.DataField("ResourceName").SetValue(item.ResourceName);
                    }

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult CompleteMaintenance(CompleteMaintenanceRequest request)
        {
            SubmitResult result = new SubmitResult();
            if (request != null)
            {
                try
                {

                    if (!string.IsNullOrEmpty(request.Password))
                    {
                        _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                    }
                    string serviceName = "CompleteMaintenance";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();

                    ICsiObject inputData = oService.InputData();
                    inputData.DataField("ForceMaintenance").SetValue("false");
                    inputData.RevisionedObjectField("MaintenanceReq").SetRef(request.MaintenanceReqName, "1", false);
                    inputData.NamedObjectField("Resource").SetRef(request.ResourceName);


                    ICsiSubentityList inputDetails = (ICsiSubentityList)inputData.SubentityList("ServiceDetails");
                    ICsiSubentity DetailsItem;
                    DetailsItem = (ICsiSubentity)inputDetails.AppendItem();
                    var list = DetailsItem.SubentityList("Checklist");
                    if (request.CheckList != null && request.CheckList.Count > 0)
                    {
                        foreach (var item in request.CheckList)
                        {
                            var checkInfo = list.AppendItem();
                            checkInfo.DataField("ChecklistId").SetValue(item.ChecklistId);
                            checkInfo.DataField("Instruction").SetValue(item.Instruction);
                            checkInfo.DataField("W_IsResult").SetValue(item.W_IsResult);
                        }
                    }

                    DetailsItem.SubentityField("MaintenanceStatus").SetObjectId(request.MaintenanceStatus);
                    if (request.DataPointList != null && request.DataPointList.Count > 0)
                    {
                        var parametricData = inputData.SubentityField("ParametricData");
                        parametricData.SetAttribute("__action", "create");
                        parametricData.SetAttribute("__CDOTypeName", "DataPointSummary");


                        ICsiSubentityList inputDetails2 = parametricData.SubentityList("DataPointDetails");
                        foreach (var item in request.DataPointList)
                        {
                            ICsiSubentity DetailsItem1;
                            DetailsItem1 = (ICsiSubentity)inputDetails2.AppendItem();

                            var csiNamedObject = DetailsItem1.NamedSubentityField("DataPoint");
                            csiNamedObject.SetName(item.DataPointName);
                            var iCsiParentInfo = csiNamedObject.ParentInfo();
                            iCsiParentInfo.SetObjectType("UserDataCollectionDef");
                            iCsiParentInfo.SetRevisionedObjectRef(item.DataCollectionDefName, "1", false);

                            DetailsItem1.DataField("DataName").SetValue(item.DataPointName);
                            switch (item.DataType)
                            {
                                case "Integer":
                                    DetailsItem1.DataField("DataType").SetValue("1");
                                    break;
                                case "Float":
                                    DetailsItem1.DataField("DataType").SetValue("2");
                                    break;
                                case "Fixed":
                                    DetailsItem1.DataField("DataType").SetValue("3");
                                    break;
                                case "String":
                                    DetailsItem1.DataField("DataType").SetValue("4");
                                    break;
                                case "Object":
                                    DetailsItem1.DataField("DataType").SetValue("5");
                                    break;
                                case "TimeStamp":
                                    DetailsItem1.DataField("DataType").SetValue("6");
                                    break;
                                case "Boolean":
                                    DetailsItem1.DataField("DataType").SetValue("7");
                                    break;
                                default:
                                    DetailsItem1.DataField("DataType").SetValue("9");
                                    break;
                            }
                            DetailsItem1.DataField("LowerLimit").SetValue(item.LowerLimit);
                            DetailsItem1.DataField("UpperLimit").SetValue(item.LowerLimit);
                            DetailsItem1.DataField("IsLimitOverrideAllowed").SetValue(item.LimitOverrideAllowed);
                            DetailsItem1.DataField("DataValue").SetValue(item.DataValue);
                            DetailsItem1.DataField("IsRequired").SetValue(item.IsRequired);
                        }

                    }

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);
                    //if (oResult.ResultCode == "1" && request.Count != 0 && request.CheckList != null && (request.Count == request.CheckList.Count))
                    //{
                    //    oResult.ResultMsg += "Checklist is completed!";
                    //    serviceName = "CompleteMaintenance";
                    //    _camstarXmlClient.InitializeSession();
                    //    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    //    oService = _camstarXmlClient.GetService();

                    //    inputData = oService.InputData();
                    //    inputData.DataField("ForceMaintenance").SetValue("false");
                    //    inputData.RevisionedObjectField("MaintenanceReq").SetRef(request.MaintenanceReqName, "1", false);
                    //    inputData.NamedObjectField("Resource").SetRef(request.ResourceName);

                    //    /*inputData.DataField("Biz_InspectAttach").SetValue(request.Biz_InspectAttach);
                    //    inputData.DataField("Biz_InspectRemark").SetValue(request.Biz_InspectRemark);
                    //    inputData.DataField("Biz_TaskCloseTime").SetFormattedValue(DateTime.Now.ToString("yyyy-MM-dd HH:mm"), DataFormats.FormatDateAndTime);
                    //    inputData.DataField("Biz_ActualWorkingHour").SetValue(request.Biz_ActualWorkingHour);
                    //    inputData.DataField("Biz_EquipMaintTask").SetValue(name);
                    //    inputData.NamedObjectField("Biz_Inspector").SetRef(request.Biz_Inspector);
                    //    inputData.RevisionedObjectField("Biz_DataCollectionDef").SetRef(request.DataPointList[0].DataCollectionDefName, "1", false);*/

                    //    inputDetails = (ICsiSubentityList)inputData.SubentityList("ServiceDetails");
                    //    ICsiSubentity details;
                    //    details = (ICsiSubentity)inputDetails.AppendItem();
                    //    details.SubentityList("Checklist");
                    //    details.SubentityField("MaintenanceStatus").SetObjectId(request.MaintenanceStatus);
                    //    oService.SetExecute();
                    //    requestData = oService.RequestData();

                    //    requestData.RequestField("CompletionMsg");

                    //    // Submit to server
                    //    respDoc = _camstarXmlClient.GetDocument().Submit();

                    //    oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    //}
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public dynamic SearchMaintenanceManagement(MaintenanceManagementRequest request)
        {
            List<MaintenanceManagementResult>? list = new List<MaintenanceManagementResult>();
            var result = new { total = list.Count, rows = list, status = "", message = "" };
            if (request != null || request.ResourceName != null)
            {
                try
                {
                    if (string.IsNullOrEmpty(request.NoState)
                       && string.IsNullOrEmpty(request.PastDue)
                       && string.IsNullOrEmpty(request.Due)
                       && string.IsNullOrEmpty(request.Pending)
                       && string.IsNullOrEmpty(request.ResourceGroupName)
                       && string.IsNullOrEmpty(request.ResourceName))
                    {
                        result = new { total = list.Count, rows = list, status = "0", message = "请选择至少一个查询条件。" };
                        return result;
                    }

                    if (!string.IsNullOrEmpty(request.Password))
                    {
                        _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                    }
                    string serviceName = "GetMaintenanceStatuses";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();

                    ICsiObject inputData = oService.InputData();
                    //if (!string.IsNullOrWhiteSpace(request.ResourceGroupName))
                    //{
                    //    inputData.DataField("ResourceGroupName").SetValue(request.ResourceGroupName);
                    //}
                    if (!string.IsNullOrWhiteSpace(request.ResourceName))
                    {
                        inputData.NamedObjectField("Resource").SetRef(request.ResourceName);
                    }
                    if (!string.IsNullOrEmpty(request.NoState))
                    {
                        inputData.DataField("NoState").SetValue(request.NoState);

                    }
                    if (!string.IsNullOrEmpty(request.PastDue))
                    {
                        inputData.DataField("PastDue").SetValue(request.PastDue);
                    }
                    if (!string.IsNullOrEmpty(request.Due))
                    {
                        inputData.DataField("WithinTolerance").SetValue(request.Due);
                    }
                    if (!string.IsNullOrEmpty(request.Pending))
                    {
                        inputData.DataField("WithinWarning").SetValue(request.Pending);
                    }
                    oService.SetExecute();

                    ICsiRequestData requestData = oService.RequestData();
                    requestData.RequestField("CompletionMsg").ToString();
                    requestData.RequestField("StatusDetails");
                    ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                    SubmitResult submit = _camstarXmlClient.CheckForErrors(respDoc);
                    if (submit.ResultCode == "1")
                    {
                        ICsiResponseData response = respDoc.GetService().ResponseData();
                        ICsiList iCsiResultList = (ICsiList)response.GetResponseFieldByName("StatusDetails");
                        if (iCsiResultList != null)
                        {
                            var numbers = iCsiResultList.GetListItems();
                            if (numbers != null)
                            {
                                foreach (var item in numbers)
                                {
                                    MaintenanceManagementResult entity = new MaintenanceManagementResult();
                                    var statusDetails = ((ICsiSubentity)item).GetAllChildren();
                                    foreach (var item1 in statusDetails)
                                    {
                                        var type = item1.GetType();
                                        string elementName = "";
                                        switch (type.Name)
                                        {
                                            case "CsiDataField":
                                                elementName = item1.GetElementName();
                                                if (elementName == "MaintenanceState")
                                                {
                                                    string? maintenanceState = item1.AsDataField().GetValue();
                                                    entity.MaintenanceState = maintenanceState;
                                                }
                                                else if (elementName == "MaintenanceType")
                                                {
                                                    string? maintenanceType = item1.AsDataField().GetValue();
                                                    entity.MaintenanceType = maintenanceType;
                                                }
                                                else if (elementName == "NextDateDue")
                                                {
                                                    string? nextDateDue = item1.AsDataField().GetValue();
                                                    entity.NextDateDue = !string.IsNullOrWhiteSpace(nextDateDue) ? Convert.ToDateTime(nextDateDue).ToString("yyyy-MM-dd HH:mm:ss") : null;
                                                }
                                                else if (elementName == "NextThruputQtyDue")
                                                {
                                                    string? nextThruputQtyDue = item1.AsDataField().GetValue();
                                                    entity.NextThruputQtyDue = nextThruputQtyDue;
                                                }
                                                break;
                                            case "CsiNamedObject":
                                                elementName = item1.GetElementName();
                                                if (elementName == "Resource")
                                                {
                                                    string? resourceName = item1.AsNamedObject().GetRef();
                                                    entity.ResourceName = resourceName;
                                                }
                                                break;
                                            case "CsiRevisionedObject":
                                                elementName = item1.GetElementName();
                                                if (elementName == "MaintenanceReq")
                                                {
                                                    string? maintenanceReqName = item1.AsRevisionedObject().GetName();
                                                    string? revision = item1.AsRevisionedObject().GetRevision();
                                                    entity.MaintenanceReqName = maintenanceReqName;
                                                    entity.MaintenanceRevision = revision;
                                                }
                                                break;
                                            case "CsiSubentity":
                                                elementName = item1.GetElementName();
                                                if (elementName == "MaintenanceStatus")
                                                {
                                                    var maintenanceStatus = item1.AsSubentity().GetAllChildren();
                                                    foreach (var items in maintenanceStatus)
                                                    {
                                                        if (items.GetType().Name == "CsiDataField")
                                                        {
                                                            var maintenanceStatusId = items.AsDataField().GetValue();
                                                            entity.MaintenanceStatus = maintenanceStatusId;
                                                        }

                                                    }
                                                }
                                                break;
                                        }
                                    }

                                    list.Add(entity);
                                }
                            }

                        }
                        _camstarXmlClient.CleanUp(serviceName + "Doc");
                        result = new { total = list.Count, rows = list, status = "1", message = "" };
                        return result;
                    }
                    else
                    {
                        list = new List<MaintenanceManagementResult>();
                        result = new { total = list.Count, rows = list, status = "0", message = submit.ResultMsg };
                        _camstarXmlClient.CleanUp(serviceName + "Doc");
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    list = new List<MaintenanceManagementResult>();
                    result = new { total = list.Count, rows = list, status = "0", message = ex.Message.ToString() };
                    return result;
                }
            }
            else
            {
                result = new { total = list.Count, rows = list, status = "0", message = "Data cannot be empty" };
                return result;
            }
        }

        /// <summary>
        /// 将时间串换算成小数
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public double ConvertTimeToDays(string time)
        {
            string[] parts = time.Split(':');
            int day = int.Parse(parts[0]);
            int hours = int.Parse(parts[1]);
            int minutes = int.Parse(parts[2]);
            int seconds = int.Parse(parts[3]);

            TimeSpan timeSpan = new TimeSpan();
            timeSpan = TimeSpan.Zero;
            timeSpan = new TimeSpan(day, hours, minutes, seconds);
            return timeSpan.TotalDays;
        }

        /// <summary>
        /// 将小数换算成时间串
        /// </summary>
        /// <param name="number"></param>
        /// <returns></returns>
        public string ConvertToTimeFormat(double number)
        {
            var timeSpan = TimeSpan.FromDays(number);
            string time = timeSpan.Days.ToString().PadLeft(3, '0') + ":" + timeSpan.Hours.ToString().PadLeft(2, '0') + ":" + timeSpan.Minutes.ToString().PadLeft(2, '0') + ":" + timeSpan.Seconds.ToString().PadLeft(2, '0');
            return time;
        }

        public SubmitResult AddJobOrder(JobOrderRequest jobOrderRequest)
        {
            SubmitResult result = new SubmitResult();
            if (jobOrderRequest != null)
            {
                try
                {
                    //维修单规则为IMR+年月日（6位）+流水码（4位）
                    SubmitResult numberingRule = GetNumberingRule("维修单规则", "true", "1");
                    string name = "";
                    if (numberingRule != null)
                    {
                        if (numberingRule.ResultCode == "1")
                        {
                            List<string> res = (List<string>)numberingRule.ResultData;
                            name = res[0];
                        }
                        else
                        {
                            result.ResulTest = "fail";
                            result.ResultCode = "0";
                            result.ResultMsg = numberingRule.ResultMsg;
                            result.ResultData = "";
                            return result;
                        }
                    }
                    else
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "NumberingRule is Not Found!";
                        result.ResultData = "";
                        return result;
                    }

                    string serviceName = "JobOrderMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    oService.Perform("New");

                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Name").SetValue(name);
                    objectChanges.NamedObjectField("Resource").SetRef(jobOrderRequest.ResourceName);
                    objectChanges.NamedObjectField("YP_EquipIMRSup").SetRef(jobOrderRequest.Biz_EquipIMRSup);
                    objectChanges.DataField("Description").SetValue(jobOrderRequest.Description);
                    objectChanges.DataField("YP_RepairingAttach").SetValue(jobOrderRequest.Biz_RepairingAttach);
                    objectChanges.DataField("YP_TaskStartTime").SetFormattedValue(DateTime.Now.ToString("yyyy-MM-dd HH:mm"), DataFormats.FormatDateAndTime);
                    objectChanges.NamedObjectField("YP_RepairingApplicant").SetRef(jobOrderRequest.Biz_RepairingApplicant);
                    objectChanges.NamedObjectField("YP_TaskStatus").SetRef("申请中");

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");
                    if (oResult.ResultCode == "1")
                    {

                        string serviceName2 = "ResourceSetupTransition";
                        _camstarXmlClient.InitializeSession();
                        _camstarXmlClient.CreateDocumentandService(serviceName2 + "Doc", serviceName2);
                        ICsiService oService3 = _camstarXmlClient.GetService();

                        ICsiObject inputData3 = oService3.InputData();
                        inputData3.DataField("Availability").SetValue("2");
                        inputData3.DataField("ES_UpdateAll").SetValue("false");
                        //string sql = @" select rd.resourcename as ResourceName
                        //                              from a_joborder j
                        //                             inner join resourcedef rd
                        //                                on j.resourceid = rd.resourceid
                        //                             where j.jobordername = @jobOrderName ";
                        //DynamicParameters Ps = new DynamicParameters();
                        //Ps.Add("@jobOrderName", jobOrderConfirmRequest.JobOrderName);
                        //var resourceList = DBServerProvider.SqlDapper.QueryList<Resource>(sql, Ps);
                        inputData3.NamedObjectField("Resource").SetRef(jobOrderRequest.ResourceName);
                        inputData3.NamedObjectField("ResourceStatusCode").SetRef("RepairStatus");
                        inputData3.DataField("UpdateTools").SetValue("true");

                        oService3.SetExecute();
                        ICsiRequestData requestData3;
                        requestData3 = oService3.RequestData();

                        requestData3.RequestField("CompletionMsg");

                        // Submit to server
                        ICsiDocument respDoc3;
                        respDoc3 = _camstarXmlClient.GetDocument().Submit();

                        var oResult3 = _camstarXmlClient.CheckForErrors(respDoc);
                        //_camstarXmlClient.CleanUp(serviceName + "Doc");
                        _camstarXmlClient.CleanUp(serviceName2 + "Doc");

                        if (oResult3.ResultCode == "1")
                        {
                            var resul = SendJobOerder(jobOrderRequest);
                            if (resul.Result != 1)
                            {
                                result.ResultMsg = resul.Message;
                            }

                        }
                        else
                        {
                            return oResult3;

                        }

                    }

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult GetNumberingRule(string ruleName, string IsBatch = "false", string BatchCount = "0")
        {
            List<string> numberList = new List<string>();
            SubmitResult result = new SubmitResult();
            try
            {
                string serviceName = "YP_GetNumberingRule";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputData = oService.InputData();
                inputData.NamedObjectField("YP_NumberingRule").SetRef(ruleName);
                inputData.DataField("IsBatch").SetValue(IsBatch);
                inputData.DataField("BatchCount").SetValue(BatchCount);

                oService.SetExecute();

                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg").ToString();
                if (IsBatch == "false")
                {
                    requestData.RequestField("Result");
                }
                else
                {
                    requestData.RequestField("ResultList");
                }

                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                result = _camstarXmlClient.CheckForErrors(respDoc);

                if (result.ResultCode == "1")
                {
                    ICsiResponseData response = respDoc.GetService().ResponseData();
                    if (IsBatch == "false")
                    {
                        ICsiDataField iCsiResult = (ICsiDataField)response.GetResponseFieldByName("Result");
                        if (iCsiResult != null)
                        {
                            numberList.Add(iCsiResult.GetValue());
                        }

                    }
                    else
                    {
                        ICsiList iCsiResultList = (ICsiList)response.GetResponseFieldByName("ResultList");
                        if (iCsiResultList != null)
                        {
                            var numbers = iCsiResultList.GetListItems();
                            foreach (var item in numbers)
                            {
                                numberList.Add(((ICsiDataField)item).GetValue());
                            }
                        }
                    }

                    result.ResultData = numberList;
                }

                _camstarXmlClient.CleanUp(serviceName + "Doc");
            }
            catch (System.Exception ex)
            {
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
            }
            return result;
        }

        /// <summary>
        /// 维修派工和取消
        /// </summary>
        /// <param name="jobOrderConfirmRequest"></param>
        /// <returns></returns>
        public SubmitResult ConfirmJobOrder(JobOrderConfirmRequest jobOrderConfirmRequest)
        {
            SubmitResult result = new SubmitResult();
            if (jobOrderConfirmRequest != null)
            {
                try
                {
                    string serviceName = "JobOrderMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputData2 = oService.InputData();
                    inputData2.NamedObjectField("ObjectToChange").SetRef(jobOrderConfirmRequest.JobOrderName);

                    oService.Perform("Load");
                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Name").SetValue(jobOrderConfirmRequest.JobOrderName);
                    objectChanges.NamedObjectField("JobModel").SetRef(jobOrderConfirmRequest.JobModel);
                    if (!string.IsNullOrWhiteSpace(jobOrderConfirmRequest.YP_EquipRepairExpenseType))
                    {
                        objectChanges.NamedObjectField("YP_EquipRepairExpenseType").SetRef(jobOrderConfirmRequest.YP_EquipRepairExpenseType);
                    }
                    if (jobOrderConfirmRequest.IsCanCancel == "0")
                    {
                        objectChanges.NamedObjectField("YP_TaskStatus").SetRef("进行中");
                    }
                    else
                    {
                        if (jobOrderConfirmRequest.YP_TaskStatus != "已取消")
                        {
                            objectChanges.NamedObjectField("YP_TaskStatus").SetRef("进行中");
                        }
                        else
                        {
                            objectChanges.NamedObjectField("YP_TaskStatus").SetRef("已取消");
                        }
                    }
                    objectChanges.NamedObjectField("YP_EquipIMRTech").SetRef(jobOrderConfirmRequest.YP_EquipIMRTech);

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    //Clean up
                    //_camstarXmlClient.CleanUp(serviceName + "Doc");

                    if (oResult.ResultCode == "1")
                    {
                        if (jobOrderConfirmRequest.IsCanCancel != "0")
                        {
                            if (jobOrderConfirmRequest.YP_TaskStatus != "已取消")
                            {
                                string serviceName2 = "ResourceSetupTransition";
                                // _camstarXmlClient.InitializeSession();
                                _camstarXmlClient.CreateDocumentandService(serviceName2 + "Doc", serviceName2);
                                ICsiService oService3 = _camstarXmlClient.GetService();

                                ICsiObject inputData3 = oService3.InputData();
                                inputData3.DataField("Availability").SetValue("2");
                                inputData3.DataField("ES_UpdateAll").SetValue("false");
                                string sql = @" select rd.resourcename as ResourceName
                                                      from a_joborder j
                                                     inner join resourcedef rd
                                                        on j.resourceid = rd.resourceid
                                                     where j.jobordername = @jobOrderName ";
                                DynamicParameters Ps = new DynamicParameters();
                                Ps.Add("@jobOrderName", jobOrderConfirmRequest.JobOrderName);
                                var resourceList = DBServerProvider.SqlDapper.QueryList<Resource>(sql, Ps);
                                inputData3.NamedObjectField("Resource").SetRef(resourceList[0].ResourceName);
                                inputData3.NamedObjectField("ResourceStatusCode").SetRef("Used");
                                inputData3.DataField("UpdateTools").SetValue("true");

                                oService3.SetExecute();
                                ICsiRequestData requestData3;
                                requestData3 = oService3.RequestData();

                                requestData3.RequestField("CompletionMsg");

                                // Submit to server
                                ICsiDocument respDoc3;
                                respDoc3 = _camstarXmlClient.GetDocument().Submit();

                                var oResult3 = _camstarXmlClient.CheckForErrors(respDoc);
                                //_camstarXmlClient.CleanUp(serviceName + "Doc");
                                _camstarXmlClient.CleanUp(serviceName2 + "Doc");
                                return oResult3;
                            }
                        }
                    }
                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult CarryOutJobOrder(JobOrderCarryOutRequest jobOrderCarryOutRequest)
        {
            SubmitResult result = new SubmitResult();
            if (jobOrderCarryOutRequest != null)
            {
                try
                {
                    string serviceName = "JobOrderMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputData2 = oService.InputData();
                    inputData2.NamedObjectField("ObjectToChange").SetRef(jobOrderCarryOutRequest.JobOrderName);

                    oService.Perform("Load");
                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Name").SetValue(jobOrderCarryOutRequest.JobOrderName);
                    objectChanges.NamedObjectField("JobSymptomCode").SetRef(jobOrderCarryOutRequest.JobSymptomCode);
                    objectChanges.NamedObjectField("JobCauseCode").SetRef(jobOrderCarryOutRequest.JobCauseCode);
                    objectChanges.NamedObjectField("JobRepairCode").SetRef(jobOrderCarryOutRequest.JobRepairCode);
                    objectChanges.NamedObjectField("YP_TaskStatus").SetRef("已完成");
                    objectChanges.DataField("YP_ActuralWorkingHour").SetValue(jobOrderCarryOutRequest.YP_ActuralWorkingHour.ToString());
                    string sql = @"SELECT
                                      brd.YP_REPAIRINGDETAILNAME as ""RepairingDetailName"",
                                      brd.CONTENT as ""Content""
                                    FROM
                                      YP_RepairingDetail brd
                                      LEFT JOIN A_JOBORDER jo ON brd.JOBORDERID = jo.JOBORDERID 
                                      WHERE jo.JOBORDERNAME = @jobOrderName ";
                    DynamicParameters Ps = new DynamicParameters();
                    Ps.Add("@jobOrderName", jobOrderCarryOutRequest.JobOrderName);
                    var list = DBServerProvider.SqlDapper.QueryList<YP_RepairingDetail>(sql, Ps);
                    //ICsiSubentityList subentityList = objectChanges.SubentityList("YP_RepairingDetail");
                    //if (list.Count > 0)
                    //{

                    //    for (int i = 0; i < list.Count; i++)
                    //    {
                    //        subentityList.DeleteItemByIndex(0);
                    //    }

                    //}
                    //foreach (YP_RepairingDetail child in jobOrderCarryOutRequest.Biz_RepairingDetailList)
                    //{
                    //    ICsiSubentity subentity = subentityList.AppendItem();
                    //    subentity.DataField("Name").SetValue(child.RepairingDetailName);
                    //    subentity.DataField("Content").SetValue(child.Content);
                    //}

                    objectChanges.DataField("YP_RepairingAttach").SetValue(jobOrderCarryOutRequest.YP_RepairingAttach);
                    objectChanges.DataField("YP_RepairingRemark").SetValue(jobOrderCarryOutRequest.YP_RepairingRemark);
                    string biz_TaskCloseTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                    objectChanges.DataField("YP_TaskCloseTime").SetFormattedValue(biz_TaskCloseTime, DataFormats.FormatDateAndTime);
                    objectChanges.DataField("YP_RepairingRemark").SetValue(jobOrderCarryOutRequest.YP_RepairingRemark);
                    //计算工时
                    TimeSpan timeDifference = Convert.ToDateTime(biz_TaskCloseTime) - Convert.ToDateTime(jobOrderCarryOutRequest.YP_TaskStartTime);
                    double biz_OrderWorkingHour = timeDifference.TotalMinutes;
                    objectChanges.DataField("YP_OrderWorkingHour").SetValue(biz_OrderWorkingHour.ToString());

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    //Clean up
                    //_camstarXmlClient.CleanUp(serviceName + "Doc");
                    if (oResult.ResultCode == "1")
                    {
                        //string serviceName2 = "ResourceSetupTransition";
                        //// _camstarXmlClient.InitializeSession();
                        //_camstarXmlClient.CreateDocumentandService(serviceName2 + "Doc", serviceName2);
                        //ICsiService oService3 = _camstarXmlClient.GetService();

                        //ICsiObject inputData3 = oService3.InputData();
                        //inputData3.DataField("Availability").SetValue("1");
                        //inputData3.DataField("ES_UpdateAll").SetValue("false");
                        //string sql3 = @" select rd.resourcename as ResourceName
                        //                              from a_joborder j
                        //                             inner join resourcedef rd
                        //                                on j.resourceid = rd.resourceid
                        //                             where j.jobordername = @jobOrderName";
                        //DynamicParameters Ps3 = new DynamicParameters();
                        //Ps3.Add("@jobOrderName", jobOrderCarryOutRequest.JobOrderName);
                        //var resourceList = DBServerProvider.SqlDapper.QueryList<Resource>(sql3, Ps3);
                        //inputData3.NamedObjectField("Resource").SetRef(resourceList[0].ResourceName);
                        //inputData3.NamedObjectField("ResourceStatusCode").SetRef("Used");
                        //inputData3.DataField("UpdateTools").SetValue("true");

                        //oService3.SetExecute();
                        //ICsiRequestData requestData3;
                        //requestData3 = oService3.RequestData();

                        //requestData3.RequestField("CompletionMsg");

                        //// Submit to server
                        //ICsiDocument respDoc3;
                        //respDoc3 = _camstarXmlClient.GetDocument().Submit();

                        //var oResult3 = _camstarXmlClient.CheckForErrors(respDoc3);
                        ////_camstarXmlClient.CleanUp(serviceName + "Doc");
                        //_camstarXmlClient.CleanUp(serviceName2 + "Doc");
                        //return oResult3;
                    }

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult UpdateJobOrderStatus(JobOrderApprovalRequest jobOrderRequest)
        {
            SubmitResult result = new SubmitResult();
            if (jobOrderRequest != null)
            {
                try
                {

                    string serviceName = "JobOrderMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputDataSync = oService.InputData();
                    inputDataSync.DataField("SyncName").SetValue(jobOrderRequest.JobOrderName);
                    _camstarXmlClient.GetService().Perform("Sync");

                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Name").SetValue(jobOrderRequest.JobOrderName);
                    objectChanges.NamedObjectField("YP_TaskStatus").SetRef(jobOrderRequest.Biz_TaskStatus);
                    objectChanges.DataField("YP_RepairingPO").SetValue(jobOrderRequest.Biz_RepairingPO);
                    objectChanges.DataField("YP_RepairingVendor").SetValue(jobOrderRequest.Biz_RepairingVendor);
                    objectChanges.DataField("YP_RepairingRemark").SetValue(jobOrderRequest.Biz_RepairingRemark);
                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        /// <summary>
        /// 设备生产换型（切换）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public dynamic ResourceChangeTxn(ResourceChangeRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "YP_ResourceChangeTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                inputData.NamedObjectField("Resource").SetRef(request.Resource);
                inputData.RevisionedObjectField("Product").SetRef(request.Product, "", true);
                inputData.DataField("ProgramName").SetValue(request.ProgramName);
                inputData.DataField("ChangeStartDate").SetFormattedValue(request.ChangeStartDate, Camstar.XMLClient.Enum.DataFormats.FormatDateAndTime);
                inputData.DataField("ChangeEndDate").SetFormattedValue(request.ChangeEndDate, Camstar.XMLClient.Enum.DataFormats.FormatDateAndTime);
                inputData.NamedObjectField("Employee").SetRef(request.Employee);
                inputData.DataField("Remark").SetValue(request.Remark);
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public SubmitResult Biz_PhysicalPositionAdd(PhysicalPosition physicalPosition)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                string serviceName = "PhysicalPositionMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                oService.Perform("New");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(physicalPosition.PhysicalPositionName);
                objectChanges.DataField("Description").SetValue(physicalPosition.Description);
                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult Biz_PhysicalPositionEdit(PhysicalPosition physicalPosition)
        {
            SubmitResult result = new SubmitResult();
            try
            {
                string serviceName = "PhysicalPositionMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData2 = oService.InputData();
                inputData2.NamedObjectField("ObjectToChange").SetRef(physicalPosition.PhysicalPositionName);

                oService.Perform("Load");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(physicalPosition.NewPhysicalPositionName);
                objectChanges.DataField("Description").SetValue(physicalPosition.Description);

                oService.SetExecute();

                // Request the completion message and more from the XML Application server
                ICsiRequestData requestData;
                requestData = oService.RequestData();

                requestData.RequestField("CompletionMsg");

                // Submit to server
                ICsiDocument respDoc;
                respDoc = _camstarXmlClient.GetDocument().Submit();

                var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                //Clean up
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                return oResult;
            }
            catch (Exception ex)
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = ex.Message;
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult Biz_PhysicalPositionDelete(PhysicalPosition physicalPosition)
        {
            SubmitResult result = new SubmitResult();
            if (physicalPosition != null)
            {
                try
                {
                    string serviceName = "PhysicalPositionMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);

                    ICsiObject inputData;
                    var oService = _camstarXmlClient.GetService();
                    inputData = oService.InputData();
                    inputData.NamedObjectField("ObjectToChange").SetRef(physicalPosition.PhysicalPositionName);
                    var per = oService.Perform("Delete");

                    // Execute the service
                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);

                    if (oResult.ResultMsg.Contains("Error 679484855"))
                    {
                        oResult.ResultMsg = "Physical Position has been referenced and cannot be deleted!";
                    }
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
                return result;
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty";
                result.ResultData = "";
                return result;
            }
        }


        public SubmitResult AddEquipBorrowTask(EquipBorrowTaskRequest equipBorrowTaskRequest)
        {
            SubmitResult result = new SubmitResult();
            if (equipBorrowTaskRequest != null)
            {
                try
                {
                    string msg = "";
                    foreach (var item in equipBorrowTaskRequest.YP_EquipList)
                    {
                        //查询是否有领用归还记录，若无领用则不允许归还
                        DynamicParameters Ps = new DynamicParameters();
                        string sql = @"";
                        if (item.ObjectType == "RESOURCE")
                        {
                            sql = @" select ebt.YP_equipborrowtaskname as ""TaskName"",
                                                es.YP_equipstatusname as ""EquipStatusName"",
                                                r.resourcename as ""ResourceName"",
                                                ebt.YP_equipborrowtime as ""EquipBorrowTime""
                                           from YP_EquipBorrowTask ebt
                                           left join YP_EquipList el
                                             on ebt.YP_equipborrowtaskid = el.YP_equipborrowtaskid
                                           left join resourcedef r
                                             on el.resourceid = r.resourceid
                                           left join YP_equipstatus es
                                             on ebt.YP_equipstatusid = es.YP_equipstatusid
                                          where r.resourcename = :resourceName 
                                          order by ebt.YP_equipborrowtime desc
                                          FETCH FIRST 1 ROWS ONLY";
                        }
                        else
                        {
                            sql = @" select ebt.YP_equipborrowtaskname as ""TaskName"",
                                                es.YP_equipstatusname as ""EquipStatusName"",
                                                r.resourcename as ""ResourceName"",
                                                ebt.YP_equipborrowtime as ""EquipBorrowTime""
                                           from YP_EquipBorrowTask ebt
                                           left join YP_EquipList el
                                             on ebt.YP_equipborrowtaskid = el.YP_equipborrowtaskid
                                           left join resourcedef r
                                             on el.toolid = r.resourceid
                                           left join YP_equipstatus es
                                             on ebt.YP_equipstatusid = es.YP_equipstatusid
                                          where r.resourcename = :resourceName 
                                          order by ebt.YP_equipborrowtime desc
                                          FETCH FIRST 1 ROWS ONLY";
                        }
                        Ps.Add(":resourceName", item.ResourceName);
                        var isBorrowtask = DBServerProvider.SqlDapper.QueryList<IsEquipBorrowTask>(sql, Ps);
                        if (isBorrowtask.Count > 0)
                        {
                            if (isBorrowtask[0].EquipStatusName != "归还")
                            {
                                if (equipBorrowTaskRequest.YP_EquipStatus != "归还")
                                {
                                    msg += item.ResourceName + " No return record,Unable to use; ";
                                }
                            }
                            else
                            {
                                if (equipBorrowTaskRequest.YP_EquipStatus == "归还")
                                {
                                    msg += item.ResourceName + " No record of receipt, no return; ";
                                }
                            }
                        }
                    }
                    if (msg != "")
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = msg;
                        result.ResultData = "";
                        return result;
                    }
                    string YP_EquipBorrowTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    //领用单规则为IMR+年月日（6位）+流水码（4位）
                    SubmitResult numberingRule = GetNumberingRule("维修单规则", "true", "1");
                    string name = "";
                    if (numberingRule != null)
                    {
                        if (numberingRule.ResultCode == "1")
                        {
                            List<string> res = (List<string>)numberingRule.ResultData;
                            name = res[0];
                        }
                        else
                        {
                            result.ResulTest = "fail";
                            result.ResultCode = "0";
                            result.ResultMsg = numberingRule.ResultMsg;
                            result.ResultData = "";
                            return result;
                        }
                    }
                    else
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "NumberingRule is Not Found!";
                        result.ResultData = "";
                        return result;
                    }
                    //string name = "IM20240627005";
                    string serviceName = "YP_EquipBorrowTaskTxn";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();

                    ICsiObject inputData = oService.InputData();
                    inputData.DataField("EquipBorrowTaskName").SetValue(name);
                    inputData.NamedObjectField("EquipStatus").SetRef(equipBorrowTaskRequest.YP_EquipStatus);
                    inputData.NamedObjectField("EquipBorrower").SetRef(equipBorrowTaskRequest.YP_EquipBorrower);
                    inputData.NamedObjectField("PhysicalLocation").SetRef(equipBorrowTaskRequest.YP_PhysicalLocation);
                    inputData.NamedObjectField("PhysicalPosition").SetRef(equipBorrowTaskRequest.YP_PhysicalPosition);
                    inputData.DataField("BorrowAttach").SetValue(equipBorrowTaskRequest.YP_BorrowAttach);
                    inputData.DataField("EquipRemark").SetValue(equipBorrowTaskRequest.YP_EquipRemark);
                    //"领用检验"
                    if (equipBorrowTaskRequest.DataPointList != null)
                    {
                        if (equipBorrowTaskRequest.DataPointList.Count > 0)
                        {
                            inputData.RevisionedObjectField("DataCollectionDef").SetRef(equipBorrowTaskRequest.DataPointList[0].DataCollectionDefName, "1", false);
                        }
                    }
                    inputData.NamedObjectField("EquipOperator").SetRef(equipBorrowTaskRequest.YP_EquipOperator);
                    inputData.DataField("EquipBorrowTime").SetFormattedValue(YP_EquipBorrowTime, DataFormats.FormatDateAndTime);
                    if (!string.IsNullOrWhiteSpace(equipBorrowTaskRequest.YP_MaxReturnDayQty))
                    {
                        inputData.DataField("MaxReturnDayQty").SetValue(equipBorrowTaskRequest.YP_MaxReturnDayQty);
                        string YP_EquipDueTime = (Convert.ToDateTime(YP_EquipBorrowTime).AddDays(Convert.ToDouble(equipBorrowTaskRequest.YP_MaxReturnDayQty))).ToString("yyyy-MM-dd HH:mm");
                        inputData.DataField("EquipDueTime").SetFormattedValue(YP_EquipDueTime, DataFormats.FormatDateAndTime);
                    }
                    if (equipBorrowTaskRequest.YP_EquipStatus == "归还")
                    {
                        inputData.DataField("EquipReturnTime").SetFormattedValue(YP_EquipBorrowTime, DataFormats.FormatDateAndTime);
                    }
                    if (!string.IsNullOrWhiteSpace(equipBorrowTaskRequest.YP_MatchingPN))
                    {
                        inputData.RevisionedObjectField("MatchingPN").SetRef(equipBorrowTaskRequest.YP_MatchingPN, "1", false);
                    }
                    inputData.DataField("ObjectType").SetValue(equipBorrowTaskRequest.YP_EquipList[0].ObjectType);
                    ICsiSubentityList inputDetails = (ICsiSubentityList)inputData.SubentityList("BEList");
                    foreach (var item in equipBorrowTaskRequest.YP_EquipList)
                    {
                        ICsiSubentity DetailsItem;
                        DetailsItem = (ICsiSubentity)inputDetails.AppendItem();
                        if (equipBorrowTaskRequest.YP_EquipList[0].ObjectType == "RESOURCE")
                        {
                            DetailsItem.NamedObjectField("Resource").SetRef(item.ResourceName);
                        }
                        else
                        {
                            DetailsItem.NamedObjectField("Tool").SetRef(item.ResourceName);
                        }
                    }
                    if (equipBorrowTaskRequest.DataPointList != null)
                    {
                        if (equipBorrowTaskRequest.DataPointList.Count > 0)
                        {
                            ICsiSubentityList inputDetails2 = (ICsiSubentityList)inputData.SubentityList("DataPointDetails");
                            foreach (var item in equipBorrowTaskRequest.DataPointList)
                            {
                                ICsiSubentity DetailsItem;
                                DetailsItem = (ICsiSubentity)inputDetails2.AppendItem();

                                var csiNamedObject = DetailsItem.NamedSubentityField("DataPoint");
                                csiNamedObject.SetName(item.DataPointName);
                                var iCsiParentInfo = csiNamedObject.ParentInfo();
                                iCsiParentInfo.SetObjectType("UserDataCollectionDef");
                                iCsiParentInfo.SetRevisionedObjectRef(item.DataCollectionDefName, "1", false);

                                DetailsItem.DataField("DataName").SetValue(item.DataPointName);
                                switch (item.DataType)
                                {
                                    case "Integer":
                                        DetailsItem.DataField("DataType").SetValue("1");
                                        break;
                                    case "Float":
                                        DetailsItem.DataField("DataType").SetValue("2");
                                        break;
                                    case "Fixed":
                                        DetailsItem.DataField("DataType").SetValue("3");
                                        break;
                                    case "String":
                                        DetailsItem.DataField("DataType").SetValue("4");
                                        break;
                                    case "Object":
                                        DetailsItem.DataField("DataType").SetValue("5");
                                        break;
                                    case "TimeStamp":
                                        DetailsItem.DataField("DataType").SetValue("6");
                                        break;
                                    case "Boolean":
                                        DetailsItem.DataField("DataType").SetValue("7");
                                        break;
                                    default:
                                        DetailsItem.DataField("DataType").SetValue("9");
                                        break;
                                }
                                DetailsItem.DataField("LowerLimit").SetValue(item.LowerLimit);
                                DetailsItem.DataField("UpperLimit").SetValue(item.LowerLimit);
                                DetailsItem.DataField("IsLimitOverrideAllowed").SetValue(item.LimitOverrideAllowed);
                                DetailsItem.DataField("DataValue").SetValue(item.DataValue);
                                DetailsItem.DataField("IsRequired").SetValue(item.IsRequired);
                            }
                        }

                    }

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }


        public SubmitResult AddPartBorrowTask(PartBorrowTaskRequest partBorrowTaskRequest)
        {
            SubmitResult result = new SubmitResult();
            if (partBorrowTaskRequest != null)
            {
                try
                {
                    //查询领用的设备下是否有设备Bom
                    DynamicParameters sqlPar = new DynamicParameters();
                    //string bomSql = @"SELECT PB.PRODUCTNAME AS Name
                    //                          FROM RESOURCEDEF R
                    //                          INNER JOIN A_RESOURCEBOM ARB
                    //                            ON ARB.RESOURCEBOMID = DECODE(R.BOMID,
                    //                                                          '0000000000000000',
                    //                                                          (SELECT REVOFRCDID
                    //                                                             FROM A_RESOURCEBOMBASE
                    //                                                            WHERE RESOURCEBOMBASEID = R.BOMBASEID),
                    //                                                          R.BOMID)
                    //                          INNER JOIN A_RESOURCEBOMMATERIALLISTITEM RBM 
                    //                            ON RBM.RESOURCEBOMID = ARB.RESOURCEBOMID 
                    //                          INNER JOIN PRODUCT P
                    //                            ON P.PRODUCTID = RBM.PRODUCTID 
                    //                          INNER JOIN PRODUCTBASE PB 
                    //                            ON P.PRODUCTBASEID = PB.PRODUCTBASEID 
                    //                        WHERE R.RESOURCENAME = @resourceName ";
                    string bomSql = @"SELECT PB.PRODUCTNAME AS Name
                                              FROM RESOURCEDEF R
                                              INNER JOIN A_RESOURCEBOM ARB
                                                ON ARB.RESOURCEBOMID = R.BOMId or R.BOMBASEID = ARB.ResourceBOMBaseId
                                              INNER JOIN A_RESOURCEBOMMATERIALLISTITEM RBM 
                                                ON RBM.RESOURCEBOMID = ARB.RESOURCEBOMID 
                                              INNER JOIN PRODUCT P
                                                ON P.PRODUCTID = RBM.PRODUCTID 
                                              INNER JOIN PRODUCTBASE PB 
                                                ON P.PRODUCTBASEID = PB.PRODUCTBASEID 
                                            WHERE R.RESOURCENAME = @resourceName ";
                    sqlPar.Add("@resourceName", partBorrowTaskRequest.YP_MatchingEquip);
                    var materialList = DBServerProvider.SqlDapper.QueryList<ModelingResult>(bomSql, sqlPar);
                    if (materialList.Count > 0)
                    {
                        string? resourceMaterialPart = partBorrowTaskRequest.ResourceMaterialPart;
                        var isExistResourceMaterialPartList = materialList.Find(r => r.Name == resourceMaterialPart);
                        if (isExistResourceMaterialPartList == null)
                        {
                            result.ResulTest = "fail";
                            result.ResultCode = "0";
                            result.ResultMsg = "The accessory part number is not in the BOM under the device, please confirm";
                            result.ResultData = "";
                            return result;
                        }
                    }
                    string Sql = @"select re.resourceName,ps.PartQty from ResourceDef re 
											inner join ProductionStatus ps on ps.ResourceId = re.resourceid
                                        where re.resourceName = @Name";
                    DynamicParameters sqlPar1 = new DynamicParameters();
                    sqlPar1.Add("@Name", partBorrowTaskRequest.PartName);
                    var QtySql = DBServerProvider.SqlDapper.QueryDynamicFirst(Sql, sqlPar1);
                    if (QtySql == null || QtySql.PartQty == null || QtySql.PartQty == 0 || QtySql.PartQty < int.Parse(partBorrowTaskRequest.YP_EquipBorrowQty))
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "改材料已经领用完，不能再次领用。";
                        result.ResultData = "";
                        return result;
                    }
                    string biz_EquipBorrowTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                    //领用单规则为IMR+年月日（6位）+流水码（4位）
                    SubmitResult numberingRule = GetNumberingRule("维修单规则", "true", "1");
                    string name = "";
                    if (numberingRule != null)
                    {
                        if (numberingRule.ResultCode == "1")
                        {
                            List<string> res = (List<string>)numberingRule.ResultData;
                            name = res[0];
                        }
                        else
                        {
                            result.ResulTest = "fail";
                            result.ResultCode = "0";
                            result.ResultMsg = numberingRule.ResultMsg;
                            result.ResultData = "";
                            return result;
                        }
                    }
                    else
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "NumberingRule is Not Found!";
                        result.ResultData = "";
                        return result;
                    }
                    //string name = "IM20240627005";
                    string serviceName = "YP_EquipBorrowTaskTxn";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();

                    ICsiObject inputData = oService.InputData();
                    inputData.DataField("EquipBorrowTaskName").SetValue(name);
                    inputData.NamedObjectField("EquipStatus").SetRef(partBorrowTaskRequest.YP_EquipStatus);
                    inputData.NamedObjectField("MatchingEquip").SetRef(partBorrowTaskRequest.YP_MatchingEquip);
                    inputData.NamedObjectField("EquipBorrower").SetRef(partBorrowTaskRequest.YP_EquipBorrower);
                    inputData.DataField("EquipBorrowQty").SetValue(partBorrowTaskRequest.YP_EquipBorrowQty);
                    inputData.DataField("PartBorrowActualQty").SetValue(partBorrowTaskRequest.YP_EquipBorrowQty);
                    inputData.DataField("OldPartReturn").SetValue(partBorrowTaskRequest.YP_OldPartReturn);
                    inputData.DataField("EquipRemark").SetValue(partBorrowTaskRequest.YP_EquipRemark);
                    inputData.DataField("EquipBorrowTime").SetFormattedValue(biz_EquipBorrowTime, DataFormats.FormatDateAndTime);
                    inputData.DataField("ObjectType").SetValue("PART");
                    inputData.DataField("PartQty").SetValue(partBorrowTaskRequest.PartQty);
                    inputData.RevisionedObjectField("MaterialPart").SetRef(partBorrowTaskRequest.ResourceMaterialPart, partBorrowTaskRequest.ResourceMaterialPartRevision, false);
                    ICsiSubentityList inputDetails = (ICsiSubentityList)inputData.SubentityList("BEList");
                    ICsiSubentity DetailsItem;
                    DetailsItem = (ICsiSubentity)inputDetails.AppendItem();
                    DetailsItem.NamedObjectField("Part").SetRef(partBorrowTaskRequest.PartName);

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);
                    if (oResult.ResultCode == "1")
                    {
                        DynamicParameters Ps = new DynamicParameters();
                        string sql = @" select R.RESOURCENAME    AS PartName,
                                               R.DESCRIPTION     AS Description,
                                               pb.productname    as ResourceMaterialPart,
                                               ps.partqty        as PartQty,
                                               p.minqtyreorderlimit as MinQtyReorderLimit,
                                               e.emailaddress as MinQtyReorderEmailGroup
                                          from resourcedef r
                                          inner join productionstatus ps
                                            on r.productionstatusid = ps.productionstatusid
                                          inner JOIN PRODUCT p
                                            ON p.productid = ps.productid
                                          inner JOIN PRODUCTBASE pb
                                            ON pb.productbaseid = p.productbaseid
                                          inner join a_emailgroup eg 
                                            on eg.emailgroupid = p.minqtyreorderemailgroupid
                                          inner join a_emailgroupentries ege 
                                            on eg.emailgroupid = ege.emailgroupid
                                          inner join employee e 
                                            on ege.entriesid = e.employeeid
                                         where r.objecttype = 'PART'
                                           and ps.productid is not null
                                           and R.RESOURCENAME = @resourceName ";
                        Ps.Add("@resourceName", partBorrowTaskRequest.PartName);
                        var partList = DBServerProvider.SqlDapper.QueryList<PartQueryResult>(sql, Ps);
                        string sendMsg = "";
                        List<string> recipients = new List<string>();
                        if (partList != null)
                        {
                            if (partList.Count > 0)
                            {
                                foreach (var part in partList)
                                {
                                    if (!string.IsNullOrWhiteSpace(part.MinQtyReorderEmailGroup))
                                    {
                                        recipients.Add(part.MinQtyReorderEmailGroup);
                                    }
                                }
                                //判断配件库存是否小于等于最小库存数
                                if (Convert.ToInt32(string.IsNullOrWhiteSpace(partList[0].PartQty) ? "0" : partList[0].PartQty)
                                    <= Convert.ToInt32(string.IsNullOrWhiteSpace(partList[0].MinQtyReorderLimit) ? "0" : partList[0].MinQtyReorderLimit))
                                {
                                    if (recipients.Count > 0)
                                    {
                                        sendMsg = string.Format("配件:{0},库存数已低于最小安全库存数量, 请注意!\r\n\r\nPart : {1}\r\nThe partQty is lower than the minQtyReorderLimit, please be aware!\r\n(Now: {2}, Min : {3})", partBorrowTaskRequest.PartName, partBorrowTaskRequest.PartName, partList[0].PartQty, partList[0].MinQtyReorderLimit);
                                        string? subject = "配件低于最小安全库存提醒";
                                        var sendEamilResult = SendEamil.SendEmailToMultipleRecipients(subject, sendMsg, recipients);
                                        if (sendEamilResult.ResultCode == "1")
                                        {
                                            oResult.ResultMsg += "\r\nSend Email Success;";
                                        }
                                        else
                                        {
                                            oResult.ResulTest = "fail";
                                            oResult.ResultCode = "0";
                                            oResult.ResultMsg += "\r\nSend Email Fail!Error:" + sendEamilResult.ResultMsg;
                                            oResult.ResultData = "";
                                            return oResult;
                                        }
                                    }
                                    else
                                    {
                                        oResult.ResulTest = "fail";
                                        oResult.ResultCode = "0";
                                        oResult.ResultMsg += "Send Eamil Error:" + "Part:" + partBorrowTaskRequest.PartName + " 's Material part " + partList[0].ResourceMaterialPart + " 's MinQtyReorderEmailGroup is not configured!";
                                        oResult.ResultData = "";
                                    }
                                }
                            }
                        }
                    }
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult AddPartReturnBorrowTask(PartBorrowTaskQueryInfoResult partBorrowTaskQueryInfoResult)
        {
            SubmitResult result = new SubmitResult();
            if (partBorrowTaskQueryInfoResult != null)
            {
                try
                {
                    string biz_EquipBorrowTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                    //领用单规则为IMR+年月日（6位）+流水码（4位）
                    SubmitResult numberingRule = GetNumberingRule("维修单规则", "true", "1");
                    string name = "";
                    if (numberingRule != null)
                    {
                        if (numberingRule.ResultCode == "1")
                        {
                            List<string> res = (List<string>)numberingRule.ResultData;
                            name = res[0];
                        }
                        else
                        {
                            result.ResulTest = "fail";
                            result.ResultCode = "0";
                            result.ResultMsg = numberingRule.ResultMsg;
                            result.ResultData = "";
                            return result;
                        }
                    }
                    else
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "NumberingRule is Not Found!";
                        result.ResultData = "";
                        return result;
                    }
                    //string name = "IM20240627005";
                    string serviceName = "YP_EquipBorrowTaskTxn";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();

                    ICsiObject inputData = oService.InputData();
                    inputData.DataField("EquipBorrowTaskName").SetValue(name);
                    inputData.NamedObjectField("EquipStatus").SetRef("归还");
                    //inputData.NamedObjectField("MatchingEquip").SetRef(partBorrowTaskQueryInfoResult.Biz_MatchingEquip);
                    inputData.NamedObjectField("EquipBorrower").SetRef(partBorrowTaskQueryInfoResult.EquipBorrower);
                    inputData.DataField("EquipBorrowTime").SetFormattedValue(biz_EquipBorrowTime, DataFormats.FormatDateAndTime);
                    inputData.DataField("EquipBorrowQty").SetValue(partBorrowTaskQueryInfoResult.ReturnQty);
                    inputData.DataField("PartBorrowActualQty").SetValue(partBorrowTaskQueryInfoResult.PartBorrowActualQty);
                    //inputData.DataField("OldPartReturn").SetValue(partBorrowTaskQueryInfoResult.OldPartReturn);
                    inputData.DataField("EquipRemark").SetValue(partBorrowTaskQueryInfoResult.EquipRemark);

                    inputData.DataField("ObjectType").SetValue("PART");
                    inputData.DataField("OldTaskName").SetValue(partBorrowTaskQueryInfoResult.TaskName);
                    inputData.DataField("PartQty").SetValue(partBorrowTaskQueryInfoResult.YP_PartQty);
                    inputData.RevisionedObjectField("MaterialPart").SetRef(partBorrowTaskQueryInfoResult.ResourceMaterialPart, partBorrowTaskQueryInfoResult.ResourceMaterialPartRevision, false);
                    ICsiSubentityList inputDetails = (ICsiSubentityList)inputData.SubentityList("BEList");
                    ICsiSubentity DetailsItem;
                    DetailsItem = (ICsiSubentity)inputDetails.AppendItem();
                    DetailsItem.NamedObjectField("Part").SetRef(partBorrowTaskQueryInfoResult.PartName);

                    oService.SetExecute();

                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();

                    requestData.RequestField("CompletionMsg");

                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();

                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);
                    //Clean up
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        public SubmitResult BOMMaint(string name, List<BomMaterialList> bomList)
        {
            SubmitResult submitResult = new SubmitResult();

            try
            {
                string serviceName = "BOMMaint";
                _camstarXmlClient = new CamstarXmlClient();
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(name);
                inputDataSync.DataField("SyncRevision").SetValue("1");
                _camstarXmlClient.GetService().Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Revision").SetValue("1");

                objectChanges.DataField("Name").SetValue(name);
                ICsiSubentityList items = objectChanges.SubentityList("MaterialList");

                //判断是否存在Material Item，有先删除
                int mlCount = new QueryService().GetBomMaterialList(name, "1").Data.Count;
                if (mlCount > 0)
                {
                    for (int i = 0; i < mlCount; i++)
                    {
                        items.DeleteItemByIndex(0);
                    }
                }

                for (int i = 0; i < bomList.Count; i++)
                {
                    ICsiSubentity item = items.AppendItem();
                    item.RevisionedObjectField("Product").SetRef(bomList[i].Code, "", true);
                    item.DataField("IssueControl").SetValue(bomList[i].IssueControl);

                    string qtyRequired = string.IsNullOrEmpty(bomList[i].RequiredQuantity) == true ?
                        "0" : (decimal.Parse(bomList[i].RequiredQuantity) / decimal.Parse(bomList[i].ProduceQuantity) * (1 + decimal.Parse(bomList[i].WasteRate))).ToString();
                    item.DataField("QtyRequired").SetValue(qtyRequired);
                    item.NamedObjectField("Uom").SetRef(bomList[i].Unit.Name);
                    item.RevisionedObjectField("Spec").SetRef(bomList[i].Spec, "", true);
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
            }
            catch (Exception ex)
            {
                submitResult.ResultCode = "0";
                submitResult.ResultMsg = ex.Message;
            }
            return submitResult;
        }

        /// <summary>
        /// 负责人或维修人员确认
        /// </summary>
        /// <param name="jobOrderCarryOutRequest"></param>
        /// <returns></returns>
        public SubmitResult PersonConfirmJobOrder(PersonConfirmJobRequest jobOrderCarryOutRequest)
        {
            SubmitResult result = new SubmitResult();
            if (jobOrderCarryOutRequest != null)
            {
                if (jobOrderCarryOutRequest.ServiceName == "Charge")//生产
                {
                    string sql = @"select a.JobOrderName,ts.YP_TaskStatusName,a.YP_ChargeDate from A_JobOrder a 
									inner join YP_TaskStatus ts on ts.YP_TaskStatusId = a.YP_TaskStatusId
									where a.YP_ChargeConfirms = 0
									and a.JobOrderName = @JoborderName";
                    DynamicParameters Ps3 = new DynamicParameters();
                    Ps3.Add("@jobOrderName", jobOrderCarryOutRequest.JobOrderName);
                    var resourceList = DBServerProvider.SqlDapper.QueryList<Resource>(sql, Ps3);
                    if (resourceList.Count == 0)
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = $"维修单：{jobOrderCarryOutRequest.JobOrderName} 确认异常！不能重复确认！";
                        result.ResultData = "";
                        return result;
                    }
                }
                else if (jobOrderCarryOutRequest.ServiceName == "Applicant")//维修
                {
                    string sql = @"select a.JobOrderName,ts.YP_TaskStatusName,a.YP_ChargeDate from A_JobOrder a 
									inner join YP_TaskStatus ts on ts.YP_TaskStatusId = a.YP_TaskStatusId
									where a.YP_ChargeConfirms = 1
									and a.YP_TechnicianConfirms = 0
									and a.JobOrderName = @JoborderName";
                    DynamicParameters Ps3 = new DynamicParameters();
                    Ps3.Add("@jobOrderName", jobOrderCarryOutRequest.JobOrderName);
                    var resourceList = DBServerProvider.SqlDapper.QueryList<Resource>(sql, Ps3);
                    if (resourceList.Count == 0)
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = $"维修单：{jobOrderCarryOutRequest.JobOrderName} 确认异常！不能重复确认！";
                        result.ResultData = "";
                        return result;
                    }
                }
                try
                {
                    string serviceName = "JobOrderMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputData2 = oService.InputData();
                    inputData2.NamedObjectField("ObjectToChange").SetRef(jobOrderCarryOutRequest.JobOrderName);

                    oService.Perform("Load");
                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Name").SetValue(jobOrderCarryOutRequest.JobOrderName);
                    string confirmsDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                    if (jobOrderCarryOutRequest.Charge)
                    {
                        objectChanges.DataField("YP_ChargeConfirms").SetValue("True");
                        objectChanges.DataField("YP_ChargeDate").SetFormattedValue(confirmsDate, DataFormats.FormatDateAndTime);
                        objectChanges.NamedObjectField("YP_TaskStatus").SetRef("已确认");
                    }
                    else if (jobOrderCarryOutRequest.Technical)
                    {
                        objectChanges.DataField("YP_TechnicianConfirms").SetValue("True");
                        objectChanges.DataField("YP_TechnicianDate").SetFormattedValue(confirmsDate, DataFormats.FormatDateAndTime);
                        objectChanges.NamedObjectField("YP_TaskStatus").SetRef("维修人员确认");
                    }
                    else
                    {
                        result.ResulTest = "fail";
                        result.ResultCode = "0";
                        result.ResultMsg = "负责人或维修人员确认标识为空！";
                        result.ResultData = "";
                        return result;
                    }

                    oService.SetExecute();
                    // Request the completion message and more from the XML Application server
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();
                    requestData.RequestField("CompletionMsg");
                    // Submit to server
                    ICsiDocument respDoc;
                    respDoc = _camstarXmlClient.GetDocument().Submit();
                    var oResult = _camstarXmlClient.CheckForErrors(respDoc);
                    //Clean up
                    //_camstarXmlClient.CleanUp(serviceName + "Doc");
                    if (oResult.ResultCode == "1" && jobOrderCarryOutRequest.ServiceName == "Applicant")
                    {
                        string serviceName2 = "ResourceSetupTransition";
                        // _camstarXmlClient.InitializeSession();
                        _camstarXmlClient.CreateDocumentandService(serviceName2 + "Doc", serviceName2);
                        ICsiService oService3 = _camstarXmlClient.GetService();
                        ICsiObject inputData3 = oService3.InputData();
                        inputData3.DataField("Availability").SetValue("1");
                        inputData3.DataField("ES_UpdateAll").SetValue("false");
                        string sql3 = @" select rd.resourcename as ResourceName
                                                      from a_joborder j
                                                     inner join resourcedef rd
                                                        on j.resourceid = rd.resourceid
                                                     where j.jobordername = @jobOrderName";
                        DynamicParameters Ps3 = new DynamicParameters();
                        Ps3.Add("@jobOrderName", jobOrderCarryOutRequest.JobOrderName);
                        var resourceList = DBServerProvider.SqlDapper.QueryList<Resource>(sql3, Ps3);
                        inputData3.NamedObjectField("Resource").SetRef(resourceList[0].ResourceName);
                        inputData3.NamedObjectField("ResourceStatusCode").SetRef("Used");
                        inputData3.DataField("UpdateTools").SetValue("true");

                        oService3.SetExecute();
                        ICsiRequestData requestData3;
                        requestData3 = oService3.RequestData();

                        requestData3.RequestField("CompletionMsg");

                        // Submit to server
                        ICsiDocument respDoc3;
                        respDoc3 = _camstarXmlClient.GetDocument().Submit();

                        var oResult3 = _camstarXmlClient.CheckForErrors(respDoc3);
                        //_camstarXmlClient.CleanUp(serviceName + "Doc");
                        _camstarXmlClient.CleanUp(serviceName2 + "Doc");
                        return oResult3;
                    }

                    return oResult;
                }
                catch (Exception ex)
                {
                    result.ResulTest = "fail";
                    result.ResultCode = "0";
                    result.ResultMsg = ex.Message;
                    result.ResultData = "";
                    return result;
                }
            }
            else
            {
                result.ResulTest = "fail";
                result.ResultCode = "0";
                result.ResultMsg = "Data cannot be empty!";
                result.ResultData = "";
                return result;
            }
        }

        /// <summary>
        /// Part维护
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public dynamic PartEidtTxn(CreatPartRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "PartSetup";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("Availability").SetValue("1");
                //_inputData.DataField("PartExpiryDate").SetFormattedValue(request.PartExpiryDate, DataFormats.FormatDateAndTime);
                _inputData.DataField("PartQty").SetValue(request.Qty);
                _inputData.NamedObjectField("PhysicalLocation").SetRef(request.PhysicalLocation);
                _inputData.NamedObjectField("PhysicalPosition").SetRef(request.PhysicalPosition);
                _inputData.RevisionedObjectField("Product").SetRef(request.ProductName, request.ProductRevision, false);
                var resource = _inputData.NamedObjectField("Resource");
                resource.SetRef(request.PartNo);

                _inputData.NamedObjectField("ResourceStatusCode").SetRef(request.ResourceStatusCode);
                _inputData.DataField("Comments").SetValue(request.Comments);
                _inputData.DataField("YP_SubscriptionCycle").SetValue(request.YP_SubscriptionCycle);
                _inputData.DataField("YP_UOM").SetValue(request.UOM);
                var details = _inputData.ObjectField("ResourceStatusDetails");
                details.DataField("PartDecommissioned").SetValue(request.Datails.PartDecommissioned);
                details.DataField("PartQtyInRequest").SetValue(request.Datails.PartQtyInRequest);
                details.DataField("PartDecommissioned").SetValue(request.Datails.PartDecommissioned);
                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// Part创建
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public dynamic PartCreatTxn(CreatPartRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }

                string serviceName = "PartCreate";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("Comments").SetValue(request.Comments);
                var partDetails = _inputData.ObjectField("PartDetails");
                //partDetails.DataField("PartExpiryDate").SetFormattedValue(request.PartExpiryDate, DataFormats.FormatDateAndTime);
                partDetails.DataField("PartQty").SetValue(request.Qty);
                partDetails.NamedObjectField("PhysicalLocation").SetRef(request.PhysicalLocation);
                partDetails.NamedObjectField("PhysicalPosition").SetRef(request.PhysicalPosition);
                partDetails.RevisionedObjectField("Product").SetRef(request.ProductName, request.ProductRevision, false);
                partDetails.DataField("Name").SetValue(request.PartNo);
                partDetails.NamedObjectField("Vendor").SetRef(request.Vendor);
                //partDetails.DataField("VendorModel").SetValue(request.VendorModel);
                //partDetails.NamedObjectField("VendorSerialNumber").SetRef(request.VendorSerialNumber);
                _inputData.NamedObjectField("YP_UOM").SetRef(request.UOM);
                _inputData.DataField("YP_SubscriptionCycle").SetValue(request.YP_SubscriptionCycle);
                //_inputData.NamedObjectField("ResourceStatusCode").SetRef(request.ResourceStatusCode);

                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic MaterialPartModeingTxn(MaterialPartRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            if (request.EventName != "DeleteAllRevisions" && (request.detailsData == null || request.detailsData.PartType == null))
            {
                return result.SetFail("详细数据不能为空！");
            }
            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "ResourceMaterialPartMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();

                oService.Perform(request.EventName);
                switch (request.EventName)
                {
                    case "DeleteAllRevisions":
                        {
                            var obj = _inputData.RevisionedObjectField("ObjectToChange");
                            obj.SetAttribute("__CDOTypeName", "ResourceMaterialPart");
                            obj.SetObjectId(request.Id);
                            obj.SetRef(request.Name, request.Revision, true);
                        }
                        break;
                    case "New":
                        {
                            var obj = _inputData.RevisionedObjectField("ObjectChanges");
                            obj.DataField("Description").SetValue(request.detailsData.Description);
                            obj.DataField("MinQty").SetValue(request.detailsData.MinQty);
                            obj.NamedObjectField("MinQtyReorderEmailGroup").SetRef(request.detailsData?.MinQtyReorderEmailGroup?.Name);
                            obj.DataField("Name").SetValue(request.Name);
                            obj.DataField("PartType").SetValue(((int)request.detailsData.PartType).ToString());
                            obj.DataField("Revision").SetValue(request.Revision);
                        }
                        break;
                    case "NewRev":
                        {
                            oService.InputData().NamedObjectField("BaseToChange").SetRef(request.Name);
                            var obj = _inputData.RevisionedObjectField("ObjectToChange");
                            obj.DataField("Description").SetValue(request.detailsData?.Description);
                            obj.DataField("MinQty").SetValue(request.detailsData?.MinQty);
                            obj.NamedObjectField("MinQtyReorderEmailGroup").SetRef(request.detailsData?.MinQtyReorderEmailGroup?.Name);
                            obj.DataField("Name").SetValue(request.Name);
                            obj.DataField("PartType").SetValue(((int)request.detailsData.PartType).ToString());
                            obj.DataField("Revision").SetValue(request.Revision);
                        }
                        break;
                    case "Load":
                        {

                            var obj = _inputData.RevisionedObjectField("ObjectToChange");
                            obj.SetAttribute("__CDOTypeName", "ResourceMaterialPart");
                            obj.SetObjectId(request.Id);
                            obj.SetRef(request.Name, request.Revision, true);
                            var _input = oService.InputData().NamedObjectField("ObjectChanges");
                            Type t = request.detailsData.GetType();
                            PropertyInfo[] propertyInfos = t.GetProperties();
                            foreach (PropertyInfo item in propertyInfos)
                            {
                                if (item.PropertyType == typeof(string))
                                {
                                    string name = item.Name;
                                    object? value = item.GetValue(request.detailsData);
                                    if (!string.IsNullOrEmpty(value?.ToString()))
                                    {
                                        _input.DataField(name).SetValue(value.ToString());
                                    }
                                }
                                else if (item.PropertyType.IsEnum)
                                {
                                    string name = item.Name;
                                    object? value = item.GetValue(request.detailsData);
                                    if (!string.IsNullOrEmpty(value.ToString()))
                                    {
                                        int i = (int)value;
                                        _input.DataField(name).SetValue(i.ToString());
                                    }
                                }
                                else
                                {
                                    string name = item.Name;
                                    ModelingResult? value = (ModelingResult)item.GetValue(request.detailsData);
                                    if (!string.IsNullOrEmpty(value?.Name?.ToString()))
                                    {
                                        _input.NamedObjectField(name).SetRef(value.Name.ToString());
                                    }
                                }
                            }
                        }
                        break;
                }
                oService.SetExecute();
                ICsiRequestData requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }


            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 包装管理
        /// </summary>
        /// <param name="Request"></param>
        /// <returns></returns>
        public dynamic PackingTxn(PackingRequest Request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                string serviceName = "YP_PackingTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("Action").SetValue(Request.Action);
                _inputData.DataField("ContainerName").SetValue(Request.ContainerName);
                _inputData.NamedObjectField("Container").SetRef(Request.ContainerName);
                _inputData.NamedObjectField("PrintQueue").SetRef(Request.PrintQueue);
                _inputData.DataField("Qty").SetValue(Request.Qty);
                _inputData.DataField("PackingPosition").SetValue(Request.PackingPosition);
                _inputData.NamedObjectField("NumberingRule").SetRef(Request.NumberingRule);
                _inputData.DataField("ES_SNDetail").SetValue(Request.ES_SNDetail);
                _inputData.DataField("ES_SNStatus").SetValue(Request.ES_SNStatus);
                _inputData.NamedObjectField("ContainerLevel").SetRef(Request.ContainerLevel);
                _inputData.NamedObjectField("MfgOrder").SetRef(Request.MfgOrder);
                _inputData.RevisionedObjectField("Product").SetRef(Request.Product, "", true);
                _inputData.NamedObjectField("Owner").SetRef(Request.Owner);
                _inputData.NamedObjectField("Employee").SetRef(Request.Employee);
                _inputData.NamedObjectField("StartReason").SetRef(Request.StartReason);
                _inputData.NamedObjectField("Shift").SetRef(Request.Shift);
                _inputData.RevisionedObjectField("Workflow").SetRef(Request.Workflow, "", true);
                var csiNamedObject = _inputData.NamedSubentityField("Step");
                csiNamedObject.SetName(Request.Step);
                var iCsiParentInfo = csiNamedObject.ParentInfo();
                iCsiParentInfo.SetObjectType("Workflow");
                iCsiParentInfo.SetRevisionedObjectRef(Request.Workflow, "", true);
                ICsiSubentityList Details = _inputData.SubentityList("Details");
                if (Request.Details != null && Request.Details.Count > 0)
                {
                    for (int i = 0; i < Request.Details.Count; i++)
                    {
                        ICsiSubentity Item = Details.AppendItem();
                        Item.DataField("ContainerName").SetValue(Request.Details[i].ContainerName);
                        Item.NamedObjectField("Container").SetRef(Request.Details[i].ContainerName);
                        Item.DataField("Qty").SetValue(Request.Details[i].Qty);
                    }
                }
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                requestData.RequestField("ContainerName");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                ICsiResponseData responseData = respDoc.GetService().ResponseData();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    ICsiDataField containerName = (CsiDataField)responseData.GetResponseFieldByName("ContainerName");
                    result.SetSuccess(containerName == null ? string.Empty : containerName.GetValue(), submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public SubmitResult BOMMaintV2(string name, List<BomListItem> bomList, string user, string psw)
        {
            SubmitResult submitResult = new SubmitResult();

            try
            {
                string serviceName = "BOMMaint";
                _camstarXmlClient = new CamstarXmlClient(user, psw.Trim());
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(name);
                inputDataSync.DataField("SyncRevision").SetValue("1");
                _camstarXmlClient.GetService().Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Revision").SetValue("1");

                objectChanges.DataField("Name").SetValue(name);
                ICsiSubentityList items = objectChanges.SubentityList("MaterialList");

                //判断是否存在Material Item，有先删除
                int mlCount = new QueryService().GetBomMaterialList(name, "1").Data.Count;
                if (mlCount > 0)
                {
                    for (int i = 0; i < mlCount; i++)
                    {
                        items.DeleteItemByIndex(0);
                    }
                }

                for (int i = 0; i < bomList.Count; i++)
                {
                    ICsiSubentity item = items.AppendItem();
                    item.RevisionedObjectField("Product").SetRef(bomList[i].Material, "", true);
                    item.DataField("IssueControl").SetValue(bomList[i].IssueControl);
                    item.DataField("QtyRequired").SetValue(bomList[i].QtyRequired);
                    item.NamedObjectField("Uom").SetRef(bomList[i].Uom);
                    item.RevisionedObjectField("Spec").SetRef(bomList[i].Spec, "", true);
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
            }
            catch (Exception ex)
            {
                submitResult.ResultCode = "0";
                submitResult.ResultMsg = ex.Message;
            }
            return submitResult;
        }

        public dynamic SyncProductV2(SyncProductRequest request)
        {
            string msg = string.Empty;
            SubmitResult submitResult = new SubmitResult();
            BaseResult<List<BaseResult<string>>> result = new BaseResult<List<BaseResult<string>>>();

            try
            {
                List<BaseResult<string>> resultList = new List<BaseResult<string>>();
                foreach (var item in request.Items)
                {
                    //校验BOM是否存在，存在则不创建
                    //if (!string.IsNullOrEmpty(item.ProductType))
                    //{
                    //    if (item.ProductType.Contains("成品"))
                    //    {
                    //        if (new QueryService().CheckBomExist(item.Product).Data.Count == 0)
                    //        {
                    //            BOMMaintV2(item.Product, new List<BomListItem>(), request.User, request.Password);
                    //        }
                    //    }
                    //}

                    string serviceName = "ProductMaint";
                    //_camstarXmlClient = new CamstarXmlClient();
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim());
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputDataSync = oService.InputData();
                    inputDataSync.DataField("SyncName").SetValue(item.Product);
                    inputDataSync.DataField("SyncRevision").SetValue(item.Revision);
                    oService.Perform("Sync");

                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Revision").SetValue(item.Revision);

                    objectChanges.DataField("Name").SetValue(item.Product);
                    objectChanges.DataField("Description").SetValue(item.Description);
                    objectChanges.DataField("Notes").SetValue(item.Description);
                    objectChanges.DataField("StdStartQty").SetValue(item.StdStartQty);
                    objectChanges.DataField("W_ERPType").SetValue(item.ERPType);
                    objectChanges.NamedObjectField("ProductType").SetRef(item.ProductType);
                    objectChanges.NamedObjectField("ProductFamily").SetRef(item.ProductFamily);
                    objectChanges.NamedObjectField("StdStartUOM").SetRef(item.Uom);
                    objectChanges.NamedObjectField("StdStartOwner").SetRef("PROD");
                    objectChanges.NamedObjectField("StdStartLevel").SetRef("LOT");
                    objectChanges.NamedObjectField("StdStartReason").SetRef("NORMAL");
                    objectChanges.NamedObjectField("ContainerNumberingRule").SetRef("Container Rule");
                    objectChanges.RevisionedObjectField("BOM").SetRef(item.Bom, item.BomRevision, false);
                    objectChanges.RevisionedObjectField("Workflow").SetRef(item.Workflow, item.WorkflowRevision, false);
                    ICsiRevisionedObjectList substitutes = objectChanges.RevisionedObjectList("Substitutes");
                    if (item.Substitutes.Count > 0)
                    {
                        foreach (var substitute in item.Substitutes)
                        {
                            substitutes.AppendItem(substitute.Product, substitute.Revision, false);
                        }
                    }
                    //objectChanges.NamedObjectField("ContainerNumberingRule").SetRef("Container Rule");
                    //objectChanges.NamedObjectField("DocumentSet").SetRef(item.Product);
                    //if (!string.IsNullOrEmpty(item.ProductType))
                    //{
                    //    if (item.ProductType.Contains("成品"))
                    //    {
                    //        objectChanges.RevisionedObjectField("BOM").SetRef(item.Product, "", true);
                    //    }
                    //    else
                    //    {
                    //        objectChanges.RevisionedObjectField("Workflow").SetRef("原材料", "", true);
                    //    }
                    //}

                    oService.SetExecute();
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();
                    requestData.RequestField("CompletionMsg");
                    ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                    submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                    _camstarXmlClient.CleanUp(serviceName + "Doc");
                    //msg += string.Format("product:{0}||result:{1}", item.Product, submitResult.ResultMsg) + Environment.NewLine;
                    BaseResult<string> itemResult = new BaseResult<string>();
                    if (submitResult.ResultCode == "1")
                    {
                        itemResult.SetSuccess(item.Product, submitResult.ResultMsg);
                    }
                    else
                    {
                        itemResult.SetFail(item.Product, submitResult.ResultMsg);
                    }
                    resultList.Add(itemResult);
                }
                result.SetSuccess(resultList, msg);
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic SyncMfgOrderV2(SyncMfgOrderRequest request)
        {
            string msg = string.Empty;
            SubmitResult submitResult = new SubmitResult();
            SubmitResult bomResult = new SubmitResult();
            BaseResult<List<BaseResult<string>>> result = new BaseResult<List<BaseResult<string>>>();

            try
            {
                List<BaseResult<string>> resultList = new List<BaseResult<string>>();

                foreach (var item in request.MfgOrderItems)
                {
                    string serviceName = "MfgOrderMaint";
                    CamstarXmlClient _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim());
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();
                    ICsiObject inputDataSync = oService.InputData();
                    inputDataSync.DataField("SyncName").SetValue(item.MfgOrder);
                    oService.Perform("Sync");

                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");

                    objectChanges.DataField("Name").SetValue(item.MfgOrder);
                    objectChanges.DataField("Qty").SetValue(item.Qty);
                    objectChanges.DataField("Description").SetValue(item.Description);
                    objectChanges.DataField("W_InventoryLocation").SetValue(item.InventoryLocation);
                    if (!string.IsNullOrEmpty(item.ReleaseDate))
                    {
                        objectChanges.DataField("ReleaseDate").SetFormattedValue(item.ReleaseDate, DataFormats.FormatDateAndTime);
                    }
                    if (!string.IsNullOrEmpty(item.PlannedStartDate))
                    {
                        objectChanges.DataField("PlannedStartDate").SetFormattedValue(item.PlannedStartDate, DataFormats.FormatDateAndTime);
                    }
                    if (!string.IsNullOrEmpty(item.PlannedCompletionDate))
                    {
                        objectChanges.DataField("PlannedCompletionDate").SetFormattedValue(item.PlannedCompletionDate, DataFormats.FormatDateAndTime);
                    }
                    objectChanges.NamedObjectField("ReportingFactory").SetRef(item.ReportingFactory);
                    objectChanges.NamedObjectField("OrderType").SetRef(item.OrderType);
                    objectChanges.NamedObjectField("OrderStatus").SetRef(item.OrderStatus);
                    objectChanges.NamedObjectField("UOM").SetRef(item.Uom);
                    objectChanges.NamedObjectField("W_WorkCenter").SetRef(item.WorkCenter);
                    objectChanges.NamedObjectField("W_Resource").SetRef(item.Resource);
                    objectChanges.NamedObjectField("W_Tool").SetRef(item.Tool);
                    objectChanges.NamedObjectField("W_EmployeeGroup").SetRef(item.EmployeeGroup);
                    objectChanges.RevisionedObjectField("Product").SetRef(item.Product, item.ProductRevision, false);
                    objectChanges.RevisionedObjectField("ES_ProductionBOM").SetRef(item.ProductBom, "", true);
                    item.Workflow.User = request.User;
                    item.Workflow.Password = request.Password;
                    BaseResult<string> syncWFResult = SyncWorkflow(item.Workflow);
                    if (syncWFResult.Result == 0)
                    {
                        return result.SetFail(null, syncWFResult.Message);
                    }
                    objectChanges.RevisionedObjectField("isWorkflow").SetRef(item.Workflow.Name, item.Workflow.Revision, false);

                    ICsiSubentityList subs = objectChanges.SubentityList("W_MfgOrderMaterialList");
                    //判断是否存在Material Item，有先删除
                    int mlCount = new QueryService().GetMfgOrderMaterialList(item.MfgOrder).Data.Count;
                    if (mlCount > 0)
                    {
                        for (int i = 0; i < mlCount; i++)
                        {
                            subs.DeleteItemByIndex(0);
                        }
                    }
                    for (int i = 0; i < item.BomListItems.Count; i++)
                    {
                        ICsiSubentity sub = subs.AppendItem();
                        sub.RevisionedObjectField("Product").SetRef(item.BomListItems[i].Material, "", true);
                        //sub.DataField("IssueControl").SetValue(item.BomListItems[i].IssueControl);
                        sub.DataField("QtyRequired").SetValue(item.BomListItems[i].QtyRequired);
                        sub.NamedObjectField("Uom").SetRef(item.BomListItems[i].Uom);
                        //sub.RevisionedObjectField("Spec").SetRef(item.BomListItems[i].Spec, "", true);
                    }

                    oService.SetExecute();
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();
                    requestData.RequestField("CompletionMsg");
                    ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                    submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    BaseResult<string> itemResult = new BaseResult<string>();
                    if (submitResult.ResultCode == "1")
                    {
                        itemResult.SetSuccess(item.MfgOrder, submitResult.ResultMsg);
                    }
                    else
                    {
                        itemResult.SetFail(item.MfgOrder, submitResult.ResultMsg);
                    }
                    resultList.Add(itemResult);
                }
                result.SetSuccess(resultList, msg);
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <returns></returns>
        public BaseResult<string> SendMsgForWeChat(SendMsgRequest request)
        {
            string msg = string.Empty;
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                HttpClientHelper httpClientHelper = new HttpClientHelper();
                //httpClientHelper.Url = AppSetting.WebChat.URL;
                Dictionary<string, string> valuePairs = new Dictionary<string, string>();
                string key = string.Empty;
                AppSetting.WebChat.GetType().GetProperties().ToList().ForEach(x =>
                {
                    if (x.Name == request.Key.ToString())
                    {
                        key = x.GetValue(AppSetting.WebChat).ToString();
                        return;
                    }
                });

                httpClientHelper.Url = AppSetting.WebChat.URL + key;


                string subMsg = GetMsgBody(request.MsgContent, request.MsgTitle);

                string resMsg = string.Empty;
                //将字符串subMsg小于2048个字符并发送到企业微信，如果超过2048个字符则分段发送
                if (subMsg.Length <= 2048)
                {
                    httpClientHelper.RequestJson = JsonConvert.SerializeObject(new
                    {
                        msgtype = "markdown",
                        markdown = new
                        {
                            content = subMsg,
                            mentioned_list = request.mentioned_list
                        }
                    });
                    var res = httpClientHelper.PostRequest();
                    res.Message += $"消息key:{request.Key}";
                    return res;
                }
                else
                {
                    int count = subMsg.Length / 2048;
                    int remainder = subMsg.Length % 2048;
                    for (int i = 0; i < count; i++)
                    {
                        httpClientHelper.RequestJson = JsonConvert.SerializeObject(new
                        {
                            msgtype = "markdown",
                            markdown = new
                            {
                                content = subMsg.Substring(i * 2048, 2048),
                                mentioned_list = request.mentioned_list
                            },

                        });
                        var res = httpClientHelper.PostRequest();
                        if (!res.Data.Contains("ok"))
                        {
                            res.Message += $"消息key:{request.Key}";
                            return res;
                        }
                        ;

                    }
                    if (remainder > 0)
                    {
                        httpClientHelper.RequestJson = JsonConvert.SerializeObject(new
                        {

                            msgtype = "markdown",
                            markdown = new
                            {
                                content = subMsg.Substring(count * 2048, remainder),
                                mentioned_list = request.mentioned_list
                            },
                        });
                        var res = httpClientHelper.PostRequest();
                        if (!res.Data.Contains("ok"))
                        {
                            res.Message += $"消息key:{request.Key}";
                            return res;
                        }

                    }
                    return result.SetSuccess(null, $"发送成功！,消息key:{request.Key}");
                }

            }
            catch (Exception ex)
            {
                return result.SetFail(null, $"消息key:{request.Key}异常原因：" + ex.Message);
            }

        }

        /// <summary>
        /// 获取消息内容
        /// </summary>
        /// <param name="source"></param>
        /// <param name="title"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public string GetMsgBody(List<string> source, string title)
        {
            if (source == null || source.Count == 0)
                throw new Exception("消息内容不能为空");
            StringBuilder sb = new StringBuilder();
            sb.Append(title);
            sb.Append("\n");
            foreach (var item in source)
            {
                sb.Append("<font color=\"comment\">");
                sb.Append(item);
                sb.Append("</font>");
                sb.Append("\n");
            }

            return sb.ToString();
        }

        /// <summary>
        /// 备保养到期
        /// </summary>
        /// <returns></returns>
        public dynamic GetMaintenance()
        {
            BaseResult<string> result = new BaseResult<string>();
            MaintenanceManagementRequest request = new MaintenanceManagementRequest()
            {
                PastDue = "true",
                Due = "true"
            };

            try
            {
                var re = SearchMaintenanceManagement(request);
                if (re.status == "1")
                {
                    List<string> listStr = new List<string>();
                    if (re.total > 0)
                    {
                        foreach (var item in re.rows)
                        {
                            listStr.Add(@$"设备：{item.ResourceName},维护要求：{item.MaintenanceState},到期时间：{item.NextDateDue},数量:{item.NextThruputQtyDue}");
                        }
                    }
                    SendMsgRequest sendMsg = new SendMsgRequest()
                    {
                        Key = keyValue.MESGroup01,
                        MsgContent = listStr,
                        MsgTitle = "备保养到期提醒",
                        MsgType = MsgType.text,
                        mentioned_list = new string[] { "@all" }
                    };
                    return SendMsgForWeChat(sendMsg);

                }
                else
                {
                    return result.SetFail(null, re.message);
                }
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }

        }

        /// <summary>
        /// 设备维修申请时发送消息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public BaseResult<string> SendJobOerder(JobOrderRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {
                listStr.Add(@$"设备：{request.ResourceName}");
                listStr.Add(@$"故障描述：{request.Description}");
                listStr.Add(@$"负责人：{request.Biz_EquipIMRSup}");
                listStr.Add(@$"申请人：{request.Biz_RepairingApplicant}");
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup02,
                    MsgContent = listStr,
                    MsgTitle = "设备维修申请:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }


        }

        public BaseResult<string> FAIInspectFailSend(FAIInspectionRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {

                listStr.Add(@$"首件批次：{request.Container}");
                listStr.Add(@$"检验人：{request.YP_Inspector}");
                if (!string.IsNullOrEmpty(request.Spec))
                {
                    listStr.Add(@$"检验工序：{request.Spec}");
                }
                listStr.Add(@$"产品编号：{request.Product}");
                listStr.Add(@$"设备：{request.Resource}");
                listStr.Add($@"工单号：{request.MfgOrder}");
                string resu = request.YP_FAIInspectionSituation == "1" ? "合格" : "不合格";
                listStr.Add(@$"检验结果：{resu}");
                listStr.Add(@$"检验时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                listStr.Add(@$"任务号：{request.YP_FAIInspectionTask}");
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup03,
                    MsgContent = listStr,
                    MsgTitle = "首件检检验不合格提醒:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }
        }

        public BaseResult<string> IPQCInspectFailSecd(IPQCInspectionRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {

                listStr.Add(@$"巡检批次：{request.Container}");
                listStr.Add(@$"检验人：{request.YP_Inspector}");
                if (!string.IsNullOrEmpty(request.Spec))
                {
                    listStr.Add(@$"检验工序：{request.Spec}");
                }
                listStr.Add(@$"产品编号：{request.Product}");
                listStr.Add(@$"设备：{request.Resource}");
                listStr.Add($@"工单号：{request.MfgOrder}");
                string resu = request.YP_IPQCInspectionSituation == "1" ? "合格" : "不合格";
                listStr.Add(@$"检验结果：{resu}");
                listStr.Add(@$"检验时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                listStr.Add(@$"任务号：{request.YP_IPQCInspectionTask}");
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup04,
                    MsgContent = listStr,
                    MsgTitle = "巡检检验不合格提醒:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }
        }

        public BaseResult<string> AQLInspectFailSecd(AQLInspectionRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {

                listStr.Add(@$"抽检批次：{request.Container}");
                listStr.Add($@"批次数量：{request.YP_ContainerQty}");
                listStr.Add(@$"检验人：{request.YP_Creator}");
                if (!string.IsNullOrEmpty(request.Spec))
                {
                    listStr.Add(@$"抽检工序：{request.Spec}");
                }
                listStr.Add(@$"产品编号：{request.Product}");
                listStr.Add(@$"不良率：{request.YP_DefectRate}");
                listStr.Add($@"工单号：{request.MfgOrder}");
                string resu = request.YP_JudgmentResults == "1" ? "合格" : "不合格";
                listStr.Add(@$"检验结果：{resu}");
                listStr.Add(@$"检验时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                listStr.Add(@$"抽检数量：{request.YP_AQLQty}");
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup05,
                    MsgContent = listStr,
                    MsgTitle = "抽样检验不合格提醒:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }
        }

        public BaseResult<string> LotExceptionSend(LotExceptionRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {
                listStr.Add(@$"异常批次：{request.ContainerName}");
                listStr.Add(@$"异常描述：{request.ExceptionRemark}");
                listStr.Add(@$"异常原因：{request.ExceptionReasonName}");
                listStr.Add(@$"异常登记人：{request.EmployeeName}");
                listStr.Add(@$"登记时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup06,
                    MsgContent = listStr,
                    MsgTitle = "批次异常提醒:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }
        }

        public BaseResult<string> ExcuteExceptionSend(LotRejectRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {

                listStr.Add(@$"异常处理批次：{request.ContainerName}");
                listStr.Add(@$"处理备注：{request.Remark}");
                listStr.Add(@$"异常登记人：{request.User}");
                listStr.Add(@$"处理时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                switch (request.JudgmentResult)
                {
                    case "1":
                        listStr.Add(@$"处理结果：让步接收");
                        listStr.Add($@"不良项：{request.DefectReason}");
                        //listStr.Add($@"异常责任人：{request.EmployeeName}");
                        listStr.Add($@"异常责任人：{request.FullName}");
                        listStr.Add($@"异常责任部门：{request.DeptName}");
                        listStr.Add($@"异常处理工序：{request.Spec.Name}");
                        listStr.Add($@"异常处理设备：{request.Resource.Name}");
                        break;
                    case "2":
                        listStr.Add(@$"处理结果：返工处理");
                        //listStr.Add($@"到返工流程：{request.ReworkWorkflow}");
                        listStr.Add($@"到返工流程：{request.Decription}");
                        listStr.Add($@"不良项：{request.DefectReason}");
                        //listStr.Add($@"异常责任人：{request.EmployeeName}");
                        listStr.Add($@"异常责任人：{request.FullName}");
                        listStr.Add($@"异常责任部门：{request.DeptName}");
                        listStr.Add($@"异常处理工序：{request.Spec.Name}");
                        listStr.Add($@"异常处理设备：{request.Resource.Name}");
                        break;
                    case "3":
                        listStr.Add(@$"处理结果：报废处理");
                        listStr.Add($@"不良项：{request.DefectReason}");
                        //listStr.Add($@"异常责任人：{request.EmployeeName}");
                        listStr.Add($@"异常责任人：{request.FullName}");
                        listStr.Add($@"异常责任部门：{request.DeptName}");
                        listStr.Add($@"异常处理工序：{request.Spec.Name}");
                        listStr.Add($@"异常处理设备：{request.Resource.Name}");
                        listStr.Add($@"报废数量：{request.ScrapQty}");
                        break;
                    case "4":
                        listStr.Add(@$"处理结果：误判");
                        break;
                }
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup07,
                    MsgContent = listStr,
                    MsgTitle = "异常提醒:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }
        }

        //计划生产工单完成发送消息
        public BaseResult<string> CompleOrderSend(List<MfgOrder> request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {
                foreach (var item in request)
                {
                    listStr.Add($@"工单：{item.Name}");
                    listStr.Add($@"计划开始时间：{item.PlannedStartDate}");
                    listStr.Add($@"计划完成时间：{item.PlannedCompletionDate}");
                    listStr.Add($@"实际完成时间：{DateTime.Now.ToString("yyyy-MM-dd")}");
                    listStr.Add($@"工单数量：{item.Qty}");
                    listStr.Add($@"实际生产数量：{item.FinishQty}");
                    listStr.Add(@$"报废数量：{item.ScrapQty}");
                }
                //listStr.Add(@$"工单号：{mfgOrder}");
                //listStr.Add(@$"计划完成时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup08,
                    MsgContent = listStr,
                    MsgTitle = "计划生产工单完成提醒:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }
        }

        public BaseResult<string> OverdueOrderSend(List<MfgOrder> request)
        {
            BaseResult<string> result = new BaseResult<string>();
            List<string> listStr = new List<string>();
            try
            {
                foreach (var item in request)
                {
                    listStr.Add($@"工单：{item.Name}");
                    listStr.Add($@"计划开始时间：{item.PlannedStartDate}");
                    listStr.Add($@"计划完成时间：{item.PlannedCompletionDate}");
                    //listStr.Add($@"实际完成时间：{DateTime.Now.ToString("yyyy-MM-dd")}");
                    listStr.Add($@"工单数量：{item.Qty}");
                    listStr.Add($@"实际完成数量：{item.FinishQty}");
                    listStr.Add(@$"报废数量：{item.ScrapQty}");
                    listStr.Add($@"正在生产数量:{item.WIPQty}");
                }
                //listStr.Add(@$"工单号：{mfgOrder}");
                //listStr.Add(@$"计划完成时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
                SendMsgRequest sendMsg = new SendMsgRequest()
                {
                    Key = keyValue.MESGroup08,
                    MsgContent = listStr,
                    MsgTitle = "计划生产工单逾期提醒:",
                    MsgType = MsgType.text,
                    mentioned_list = new string[] { "@all" }
                };
                return SendMsgForWeChat(sendMsg);
            }
            catch (Exception ex)
            {
                return result.SetFail(null, ex.Message);
            }
        }


        /// <summary>
        /// 获取前一天生产完成的工单
        /// </summary>
        /// <returns></returns>
        public dynamic GetCompleOrder()
        {
            string sql = $@"
                    select mfs.MfgOrderName Name
                    ,mfs.ProductName Product
                    ,mf.Qty
                    ,mf.PlannedStartDate
                    ,mf.PlannedCompletionDate
                    ,mfs.FinishQty
                    ,mfs.ScrapQty
                    ,mfs.WIPQty
					from YP_GetMfgOrderComplete('%%') mfs
					inner join MfgOrder mf on mf.MfgOrderId = mfs.mfgOrderid";
            try
            {
                var list = DBServerProvider.SqlDapper.QueryList<MfgOrder>(sql, null);
                List<MfgOrder> overdues = list.Where(s => s.WIPQty > 0 && (s.ScrapQty + s.FinishQty <= s.Qty) && s.PlannedCompletionDate < DateTime.Now).ToList();
                List<MfgOrder> completeds = list.Where(s => s.WIPQty == 0 && (s.ScrapQty + s.FinishQty >= s.Qty)).ToList();
                if (completeds.Count() > 0)
                {
                    CompleOrderSend(completeds);
                }
                if (overdues.Count() > 0)
                {
                    OverdueOrderSend(overdues);
                }
            }
            catch (Exception ex)
            {
                return new BaseResult<string>().SetFail(null, ex.Message);
            }
            return new BaseResult<string>().SetSuccess(null, "消息发送成功！");
        }

        public dynamic MaterialRequest(MaterialRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (request.RequestData == null)
                {
                    return result.SetFail(null, "领料数据不允许为空。");
                }
                var checkQty = request.RequestData.Where(x => double.Parse(x.RequestQty) <= 0).ToList();
                if (checkQty.Count() > 0)
                {
                    return result.SetFail(null, checkQty[0].Material + "领料数不能为空或小于0。");
                }

                //调用ERP获取标签
                MaterialRequisition erpRequest = new MaterialRequisition();
                erpRequest.MESID = Guid.NewGuid().ToString("N");
                erpRequest.ZTYPE = request.Type;
                List<MaterialRequisitionItem> items = new List<MaterialRequisitionItem>();
                foreach (var data in request.RequestData)
                {
                    MaterialRequisitionItem item = new MaterialRequisitionItem();
                    item.AUFNR = data.MfgOrder;
                    item.IDNRK = data.Material;
                    item.MENGE_SQ = data.RequestQty;
                    item.LGORT_I = string.Empty;// data.InventoryLocation;
                    switch (request.Type)
                    {
                        case "Z011":
                            item.SGTXT_I = string.Empty;
                            item.ZYDYY = string.Empty;
                            break;
                        case "Z012":
                            item.SGTXT_I = data.Department;
                            item.ZYDYY = data.Reason;
                            break;
                        default:
                            break;
                    }
                    items.Add(item);
                }
                erpRequest.ITEM = items;

                string url = AppSetting.ERPConfig.MATERIAL_REQUISITION;
                string json = JsonConvert.SerializeObject(erpRequest);
                BaseResult<ERPInfo> erpResult = ERPRequest(url, json);
                Logger.Info(LoggerType.ERP, json, JsonConvert.SerializeObject(erpResult));

                if (erpResult.Result == 0)
                    return result.SetFail(null, "ERP信息：" + erpResult.Message);

                //调用领料服务
                SubmitResult submitResult = new SubmitResult();
                string cdoName = DateTime.Now.ToString("yyyymmddhhmmss");

                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "W_MaterialRequestOrderMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(cdoName);
                oService.Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");

                objectChanges.DataField("Name").SetValue(cdoName);

                ICsiSubentityList subList = objectChanges.SubentityList("W_MaterialRequestOrderDetail");
                foreach (var item in request.RequestData)
                {
                    ICsiSubentity sub = subList.AppendItem();
                    sub.NamedObjectField("MfgOrder").SetRef(item.MfgOrder);
                    sub.RevisionedObjectField("Product").SetRef(item.Product, item.P_Revision, false);
                    sub.DataField("CapacityByDay").SetValue(item.CapacityByDay);
                    sub.DataField("OrderQty").SetValue(item.OrderQty);
                    sub.DataField("Production").SetValue(item.Production);
                    sub.DataField("StandardCapacity").SetValue(item.StandardCapacity);
                    sub.RevisionedObjectField("Material").SetRef(item.Material, item.M_Revision, false);
                    sub.DataField("RequestQty").SetValue(item.RequestQty);
                    sub.DataField("ApplicationTime").SetValue(DateTime.Now.ToString());
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic ExcessRequestCheck(CommonCDORequest request)
        {
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (request.requestData == null)
                {
                    return result.SetFail(null, "超领确认数据不允许为空。");
                }
                     //调用ERP消耗接口
                foreach (var item in request.requestData)
                {
                    MaterialConsumption materialConsumption = new MaterialConsumption();
                    List<MaterialConsumptionItem> materialConsumptions = new List<MaterialConsumptionItem>();
                    MaterialConsumptionItem materialConsumptionItem = new MaterialConsumptionItem();
                    materialConsumptionItem.AUFNR = item.MfgOrder;
                    materialConsumptionItem.HUIDENT = item.Container;
                    materialConsumptionItem.MENGE = item.Qty;
                    materialConsumptions.Add(materialConsumptionItem);
                    materialConsumption.MESID = Guid.NewGuid().ToString("N");
                    materialConsumption.ITEM = materialConsumptions;

                    BaseResult<ERPInfo> erpResult = ERPRequest(AppSetting.ERPConfig.MATERIAL_CONSUMPTION, JsonConvert.SerializeObject(materialConsumption));
                    Logger.Info(LoggerType.ERP, JsonConvert.SerializeObject(materialConsumption), JsonConvert.SerializeObject(erpResult));
                    if (erpResult.Result == 0)
                        return result.SetFail(null, "ERP信息：" + erpResult.Message);
                }

                var groupData = request.requestData.GroupBy(x => new { x.CDOName, x.CDOID }).ToList();
                //只支持一组
                foreach (var group in groupData)
                {
                    SubmitResult submitResult = new SubmitResult();
                    var itemData = request.requestData[0];

                    if (!string.IsNullOrEmpty(request.Password))
                    {
                        _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                    }
                    string serviceName = "W_MaterialOutForExcessMaint";
                    _camstarXmlClient.InitializeSession();
                    _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                    ICsiService oService = _camstarXmlClient.GetService();

                    ICsiObject inputDataOTC = oService.InputData();
                    ICsiNamedObject objectToChange = inputDataOTC.NamedObjectField("ObjectToChange");
                    objectToChange.SetAttribute("__CDOTypeName", "W_MaterialOutForExcess");
                    objectToChange.SetObjectId(group.Key.CDOID);
                    objectToChange.SetRef(group.Key.CDOName);

                    oService.Perform("Load");

                    ICsiObject inputDataOC = oService.InputData();
                    ICsiNamedObject objectChange = inputDataOC.NamedObjectField("ObjectChanges");
                    ICsiSubentityList subList = objectChange.SubentityList("W_MaterialOutForExcessDetail");
                    var grouptemp = group.ToList();
                    for (int i = 0; i < grouptemp.Count(); i++)
                    {
                        ICsiSubentity sub = subList.AppendItem();
                        sub.SetAttribute("__listItemAction", "change");
                        sub.DataField("__index").SetValue(i.ToString());
                        sub.DataField("Status").SetValue("1");
                        //sub.NamedObjectField("Container").SetRef(grouptemp[i].Container);
                    }

                    oService.SetExecute();
                    ICsiRequestData requestData;
                    requestData = oService.RequestData();
                    requestData.RequestField("CompletionMsg");
                    ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                    submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                    _camstarXmlClient.CleanUp(serviceName + "Doc");

                    if (submitResult.ResultCode == "1")
                    {
                        result.SetSuccess(null, submitResult.ResultMsg);
                    }
                    else
                    {
                        result.SetFail(null, submitResult.ResultMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic MaterialIssueReport()
        {
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                //查询物料消耗记录
                string sql = @"";

                //报工

                //将报工结果存入报工查看历史表
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic LineChangeAndSetup(LineChangeAndSetupRequest request)
        {
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                //查找设备是否有未结束的调机记录
                BaseResult<List<ModelingResult>> checkResult = new QueryService().CheckLineChangeResource(request.Resource);
                if (checkResult.Result == 0)
                {
                    result.SetFail(null, checkResult.Message);
                    return result;
                }
                if (checkResult.Data.Count > 0)
                {
                    result.SetFail(null, request.Resource + "存在未结束的换线调机记录，请先结束前一任务。");
                    return result;
                }

                SubmitResult submitResult = new SubmitResult();
                string cdoName = string.Empty;
                string status = string.Empty;
                switch (request.Action)
                {
                    case "add":
                        cdoName = DateTime.Now.ToString("yyyymmddhhmmss");
                        status = "0";
                        break;
                    case "complete":
                        cdoName = request.CDOName;
                        status = "1";
                        break;
                    default:
                        break;
                }

                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "W_LineChangeAndSetupMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(cdoName);
                oService.Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");

                objectChanges.DataField("Name").SetValue(cdoName);
                switch (request.Action)
                {
                    case "add":
                        {
                            objectChanges.DataField("ChangeTypes").SetValue(request.ChangeType);
                            objectChanges.NamedObjectField("Resource").SetRef(request.Resource);
                            objectChanges.NamedObjectField("WMfgOrder").SetRef(request.MfgOrder);
                            objectChanges.NamedObjectField("Creator").SetRef(request.User);
                            objectChanges.DataField("StartTime").SetFormattedValue(DateTime.Now.ToString(),
                                DataFormats.FormatDateAndTime);
                        }
                        break;
                    case "complete":
                        {
                            objectChanges.DataField("Status").SetValue(status);
                            objectChanges.DataField("Notes").SetValue(request.Notes);
                            objectChanges.DataField("CavityCount").SetValue(request.CavityCount);
                            objectChanges.DataField("Speed").SetValue(request.Speed);
                            objectChanges.NamedObjectField("Completer").SetRef(request.User);
                            objectChanges.NamedObjectField("Department").SetRef(request.Department);
                            objectChanges.DataField("endtime").SetFormattedValue(DateTime.Now.ToString(),
                                DataFormats.FormatDateAndTime);
                            ICsiNamedObjectList subList = objectChanges.NamedObjectList("OutTimeReason");
                            foreach (var item in request.Reason)
                            {
                                ICsiNamedObject sub = subList.AppendItem(item);
                            }
                        }
                        break;
                    default:
                        break;
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic SyncWorkflow(SyncWorkflowRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (request.Steps == null || !request.Steps.Any())
                {
                    result.SetFail(null, submitResult.ResultMsg);
                    return result;
                }
                string serviceName = "WorkflowMaint";

                CamstarXmlClient _camstarXmlClient = new CamstarXmlClient(request.User, request.Password);
                BaseResult<List<CommonQueryResult>> getStep = new QueryService().GetStep(request.Name,request.Revision);

                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(request.Name);
                inputDataSync.DataField("SyncRevision").SetValue(request.Revision);
                oService.Perform("Sync");

                request.Steps = request.Steps.OrderBy(x => x.Sequnce).ToList();
                //增加仓库工序
                WorkflowStep invStep = new WorkflowStep()
                {
                    Name = "仓库",
                    Spec = "仓库",
                    Sequnce = "仓库"
                };
                request.Steps.Add(invStep);

                {
                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    objectChanges.DataField("Revision").SetValue(request.Revision);

                    objectChanges.DataField("Name").SetValue(request.Name);
                    objectChanges.DataField("Description").SetValue(request.Description);
                    objectChanges.DataField("Notes").SetValue(request.Description);
                    ICsiSubentityList steps = objectChanges.SubentityList("Steps");
                    for (int i = 0; i < request.Steps.Count; i++)
                    {
                        ICsiSubentity sub = steps.AppendItem();
                        sub.SetAttribute("__listItemAction", "add");
                        sub.SetAttribute("__CDOTypeName", "SpecStepChanges");
                        sub.DataField("Name").SetValue(request.Steps[i].Sequnce);
                        //sub.DataField("Name").SetValue(request.Steps[i].Name);
                        sub.RevisionedObjectField("Spec").SetRef(request.Steps[i].Spec, "", true);
                        sub.DataField("XLocation").SetValue((request.Steps[i].XLocation + i * 160).ToString());
                        sub.DataField("YLocation").SetValue((request.Steps[i].YLocation).ToString());
                    }

                    //删除现有工步
                    if (getStep.Result == 1)
                    {
                        if (getStep.Data.Count > 0)
                        {
                            for (int j = 0; j < getStep.Data.Count; j++)
                            {
                                //存在路径，删除路径
                                if (!string.IsNullOrEmpty(getStep.Data[j].DefaultPathID)
                                    && !string.IsNullOrEmpty(getStep.Data[j].ToStepName))
                                {
                                    ICsiSubentity changeSub = steps.AppendItem();
                                    changeSub.SetAttribute("__listItemAction", "change");
                                    changeSub.NamedObjectField("__key").SetRef(getStep.Data[j].StepName);

                                    ICsiSubentityList pathSubs = changeSub.SubentityList("Paths");
                                    ICsiSubentity pathSub = pathSubs.AppendItem();
                                    pathSub.SetAttribute("__listItemAction", "delete");
                                    pathSub.NamedObjectField("__key").SetRef(getStep.Data[j + 1].StepName);
                                }

                                ICsiSubentity deleteSub = steps.AppendItem();
                                deleteSub.SetAttribute("__listItemAction", "delete");
                                deleteSub.SetAttribute("__CDOTypeName", "SpecStepChanges");
                                deleteSub.NamedObjectField("__key").SetRef(getStep.Data[j].StepName);
                            }
                        }
                    }
                }
                {
                    ICsiObject inputData = oService.InputData();
                    ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                    var firstStep = objectChanges.NamedSubentityField("FirstStep");
                    firstStep.SetName(request.Steps[0].Sequnce);
                    var parentInfo = firstStep.ParentInfo();
                    parentInfo.SetAttribute("__CDOTypeName", "WorkflowChanges");
                    parentInfo.SetRevisionedObjectRef(request.Name, request.Revision, false);
                    ICsiSubentityList steps = objectChanges.SubentityList("Steps");

                    for (int i = 0; i < request.Steps.Count; i++)
                    {
                        ICsiSubentity sub = steps.AppendItem();
                        sub.SetAttribute("__listItemAction", "change");
                        sub.SetAttribute("__CDOTypeName", "SpecStepChanges");
                        sub.NamedObjectField("__key").SetRef(request.Steps[i].Sequnce);
                        sub.DataField("Name").SetValue(request.Steps[i].Sequnce);
                        sub.RevisionedObjectField("Spec").SetRef(request.Steps[i].Spec, "", true);
                        sub.DataField("XLocation").SetValue((request.Steps[i].XLocation + i * 160).ToString());
                        sub.DataField("YLocation").SetValue(request.Steps[i].YLocation.ToString());
                        sub.DataField("W_StandardTime").SetValue(request.Steps[i].StandardTime?.ToString());

                        //添加路径
                        if (i < request.Steps.Count - 1)
                        {
                            ICsiSubentityList paths = sub.SubentityList("Paths");
                            ICsiSubentity pathSub = paths.AppendItem();
                            pathSub.SetAttribute("__listItemAction", "add");
                            pathSub.SetAttribute("__CDOTypeName", "MovePathChanges");
                            pathSub.DataField("Name").SetValue(request.Steps[i + 1].Sequnce);
                            var toStep = pathSub.NamedSubentityField("ToStep");
                            toStep.SetName(request.Steps[i + 1].Sequnce);
                            toStep.ParentInfo().SetRevisionedObjectRef(request.Name, request.Revision, false);
                        }
                    }
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    if (submitResult.ResultMsg.Contains("Maintenance_E0021") || submitResult.ResultMsg.Contains("Maintenance_E0020"))
                    {
                        result.SetSuccess(null, submitResult.ResultMsg);
                    }
                    else
                    {
                        result.SetFail(null, submitResult.ResultMsg);
                    }
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic PackageLabelPrintTxn(CommonCDORequest request)
        {

            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                //调用ERP接口
                PackagingLabel erpRequest = new PackagingLabel();
                erpRequest.MESID = Guid.NewGuid().ToString("N");
                erpRequest.AUFNR = request.MfgOrder;
                erpRequest.MENGE = request.Qty;
                erpRequest.CHECK_QTY = request.MinPackageQty;
                erpRequest.HSDAT = DateTime.Now.Date.ToString("yyyyMMdd");
                erpRequest.MECAP = request.MaxInnerQty;
                string labelUrl = AppSetting.ERPConfig.PACKAGING_LABEL;
                string labelJson = JsonConvert.SerializeObject(erpRequest);
                BaseResult<ERPInfo> erpResult = ERPRequest(labelUrl, labelJson);
                Logger.Info(LoggerType.ERP, labelJson, JsonConvert.SerializeObject(erpResult));
                if (erpResult.Result == 0)
                {
                    return result.SetFail(null, "ERP信息：" + erpResult.Message);
                }
                else
                {
                    List<PackageLabelPrintDetail> packageLabelPrintDetails = new List<PackageLabelPrintDetail>();
                    foreach (var item in erpResult.Data.BARCODE)
                    {
                        PackageLabelPrintDetail packageLabelPrintDetail = new PackageLabelPrintDetail();
                        packageLabelPrintDetail.InnerLabelId = item.HUIDENT;
                        packageLabelPrintDetail.InnerQty = item.BZ_MENGE;
                        packageLabelPrintDetail.OuterLabelId = item.HU_PAR;
                        packageLabelPrintDetail.OuterQty = item.MECAP;
                        packageLabelPrintDetails.Add(packageLabelPrintDetail);
                    }
                    request.packageLabelPrintDetails = packageLabelPrintDetails;
                }

                string json = string.Empty;
                string serviceName = "w_packageLabelPrintTxn";
                ERPRequest<HU_PRINT> printRequest = new ERPRequest<HU_PRINT>();
                printRequest.data = new List<HU_PRINT>();

                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                inputData.NamedObjectField("RequestMfgOrder").SetRef(request.MfgOrder);
                inputData.RevisionedObjectField("Product").SetRef(request.Product, "", true);
                ICsiSubentityList innerDetails = inputData.SubentityList("W_PackageLabelPrintDetail");
                foreach (var item in request.packageLabelPrintDetails)
                {
                    ICsiSubentity sub = innerDetails.AppendItem();
                    sub.DataField("InnerLabelId").SetValue(item.InnerLabelId);
                    sub.DataField("InnerQty").SetValue(item.InnerQty);
                    sub.DataField("OuterLabelId").SetValue(item.OuterLabelId);
                    sub.DataField("OuterQty").SetValue(item.OuterQty);

                    //标签打印信息
                    HU_PRINT innerLabel = new HU_PRINT();
                    innerLabel.HU = item.InnerLabelId;
                    innerLabel.RSRC = request.Printer;
                    innerLabel.UNAME = request.User;
                    printRequest.data.Add(innerLabel);
                    if (!request.PrintOutBox)
                    {
                        HU_PRINT outerLabel = new HU_PRINT();
                        outerLabel.HU = item.OuterLabelId;
                        outerLabel.RSRC = request.Printer;
                        outerLabel.UNAME = request.User;
                        printRequest.data.Add(outerLabel);
                    }

                   
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    string url = AppSetting.ERPConfig.HU_PRINT;
                    //去重
                    printRequest.data = printRequest.data.GroupBy(item => item.HU)
                                           .Select(group => group.First())
                                           .ToList();
                    json = JsonConvert.SerializeObject(printRequest);
                    BaseResult<string> printResult = ERPPrint(url, json);
                    if (printResult.Result == 0)
                        result.SetFail(null, printResult.Message);
                    else
                        result.SetSuccess(null, printResult.Message);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic InventoryInspection(CommonCDORequest request)
        {

            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                //调用sap接口
                string ERPInspectionOrder = "123456";//未确定
                //InboundInspection inboundInspection = new InboundInspection();
                //inboundInspection.MESID = Guid.NewGuid().ToString();
                //inboundInspection.AUFNR = request.requestData[0].MfgOrder;
                //inboundInspection.MENGE = request.requestData[0].MfgOrderQty;
                //inboundInspection.CHARG = request.requestData[0].InnerLabelId;
                ////inboundInspection.LGORT = "";
                //string json = JsonConvert.SerializeObject(inboundInspection);
                //BaseResult<ERPInfo> erpRequest = ERPRequest(AppSetting.ERPConfig.INBOUND_INSPECTION, json);
                //if (erpRequest.Result == 0)
                //{
                //    return result.SetFail(null, "ERP信息："+ erpRequest.Message);
                //}
                //else
                //{
                //    ERPInspectionOrder = erpRequest.Data.PRUEFLOS;
                //}

                string serviceName = "W_InventoryInspectionTxn";
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                inputData.DataField("ERPInspectionOrder").SetValue(ERPInspectionOrder);
                inputData.DataField("Status").SetValue(request.Status);
                inputData.NamedObjectField("InspectionMfgOrder").SetRef(request.requestData.Count > 0 ? request.requestData[0].MfgOrder : "");
                ICsiSubentityList inspectionDetail = inputData.SubentityList("W_InventoryInspectionDetail");
                foreach (var item in request.requestData)
                {
                    ICsiSubentity sub = inspectionDetail.AppendItem();
                    sub.NamedObjectField("InnerLabelId").SetRef(item.InnerLabelId);
                    sub.DataField("Status").SetValue(request.Status);//inspection
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic QualityInspectionSheetMaint(CommonCDORequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "W_QualityInspectionSheetMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                switch (request.EventName)
                {
                    case "delete":
                        {
                            ICsiObject inputData = oService.InputData();
                            ICsiNamedObject objectToChange = inputData.NamedObjectField("ObjectToChange");

                            objectToChange.SetObjectType("W_QualityInspectionSheet");
                            objectToChange.SetObjectId(request.CDOID);
                            objectToChange.SetRef(request.CDOName);
                            oService.Perform("Delete");
                        }
                        break;
                    default:
                        {
                            ICsiObject inputDataSync = oService.InputData();
                            inputDataSync.DataField("SyncName").SetValue(request.CDOName);
                            oService.Perform("Sync");

                            ICsiObject inputData = oService.InputData();
                            ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");

                            objectChanges.DataField("Name").SetValue(request.CDOName);
                            objectChanges.RevisionedObjectField("Product").SetRef(request.Product, "", true);
                            objectChanges.RevisionedObjectField("Spec").SetRef(request.Spec, "", true);
                            switch (request.InspectionType)
                            {
                                case "SI":
                                    request.InspectionType = "1";
                                    break;
                                case "FAI":
                                    request.InspectionType = "2";
                                    break;
                                case "IPQC":
                                    request.InspectionType = "3";
                                    break;
                                case "FQC":
                                    request.InspectionType = "4";
                                    break;
                                default:
                                    break;
                            }
                            objectChanges.DataField("InspectionType").SetValue(request.InspectionType);
                            ICsiSubentityList inspectionDetail = objectChanges.SubentityList("W_QualityInspectionSheetDetail");

                            //判断是否存在Material Item，有先删除
                            int count = new QueryService().GetQualityInspectionSheetDetail(request.CDOName).Data.Count;
                            if (count > 0)
                            {
                                for (int i = 0; i < count; i++)
                                {
                                    inspectionDetail.DeleteItemByIndex(0);
                                }
                            }

                            foreach (var item in request.requestData)
                            {
                                ICsiSubentity sub = inspectionDetail.AppendItem();
                                sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                sub.DataField("InspectionPointContent").SetValue(item.InspectionPointContent);
                                sub.DataField("InspectionPointType").SetValue(item.InspectionPointType);
                                sub.DataField("InspectionTool").SetValue(item.InspectionTool);
                                sub.DataField("DefaultValue").SetValue(item.DefaultValue);
                                sub.DataField("LowerLimit").SetValue(item.LowerLimit);
                                sub.DataField("UpperLimit").SetValue(item.UpperLimit);
                                sub.DataField("FromIOT").SetValue(item.FromIOT);
                            }
                        }
                        break;
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic InventoryInspectionOrderMaint(CommonCDORequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {

                string serviceName = "W_InventoryInspectionOrderMaint";
                _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(request.CDOName);
                oService.Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");

                objectChanges.DataField("Name").SetValue(request.CDOName);
                switch (request.Type)
                {
                    case "NCP":
                        {
                            objectChanges.DataField("OutCome").SetValue(request.OutCome);
                            objectChanges.DataField("NCPNotes").SetValue(request.NCPNotes);
                            objectChanges.NamedObjectField("NCPEmployee").SetRef(request.User);
                            objectChanges.DataField("NCPDate").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                        }
                        break;
                    default:
                        {
                            objectChanges.DataField("AQLQty").SetValue(request.AQLQty);
                            objectChanges.DataField("Customer").SetValue(request.Customer);
                            objectChanges.DataField("IAResult").SetValue(request.IAResult);
                            objectChanges.DataField("ProductType").SetValue(request.ProductType);
                            objectChanges.DataField("Status").SetValue(request.Status);
                            objectChanges.DataField("Notes").SetValue(request.Notes);
                            objectChanges.DataField("CreateTime").SetFormattedValue(request.CreateDate, DataFormats.FormatDate);
                            objectChanges.DataField("InspectionDate").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                            objectChanges.NamedObjectField("InspectionMfgOrder").SetRef(request.MfgOrder);
                            objectChanges.NamedObjectField("Inspector").SetRef(request.User);
                            objectChanges.NamedObjectField("Creator").SetRef(request.Creator);
                            objectChanges.NamedObjectField("WarehouseReceipt").SetRef(request.CDOName);//入库单号
                            objectChanges.DataField("ProductType").SetValue(request.ProductType);

                            if (request.AllowStardardDetails != null)
                            {
                                ICsiSubentityList allowStardardDetails = objectChanges.SubentityList("W_AllowStardardDetails");

                                foreach (var item in request.AllowStardardDetails)
                                {
                                    ICsiSubentity sub = allowStardardDetails.AppendItem();
                                    sub.DataField("Stardard").SetValue(item.AllowStardard);
                                    sub.DataField("StardardMIN").SetValue(item.StardardMIN);
                                    sub.DataField("MIJ").SetValue(item.MIJ);
                                    sub.DataField("CR").SetValue(item.CR);
                                }
                            }

                            if (request.CollectDetails != null)
                            {
                                ICsiSubentityList collectDetails = objectChanges.SubentityList("W_FQCCollectDetails");

                                foreach (var item in request.CollectDetails)
                                {
                                    if (item.ActualValue != null)
                                    {
                                        foreach (var collect in item.ActualValue)
                                        {
                                            ICsiSubentity sub = collectDetails.AppendItem();
                                            sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                            sub.DataField("ActualValue").SetValue(collect.ActualValue);
                                            sub.DataField("ItemResult").SetValue(item.ItemResult == "OK" ? "1" : "0");
                                            sub.DataField("Notes").SetValue(item.Notes);
                                        }
                                    }
                                    else
                                    {
                                        ICsiSubentity sub = collectDetails.AppendItem();
                                        sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                        sub.DataField("ItemResult").SetValue(item.ItemResult == "OK" ? "1" : "0");
                                        sub.DataField("Notes").SetValue(item.Notes);
                                    }
                                }
                            }
                        }
                        break;
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic WorkReport(CommonCDORequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                //检查已报工数量
                foreach (var item in request.requestData)
                {
                    CommonQueryRequest queryRequest = new CommonQueryRequest();
                    queryRequest.mfgorder = item.MfgOrder;
                    BaseResult<CustomTableRusult> checkReport = new QueryService().GetAlreadyReport(queryRequest);
                    if (checkReport.Result == 0)
                    {
                        result.SetFail(null, checkReport.Message);
                        return result;
                    }
                    if (checkReport.Data.tableData.Count() > 0)
                    {
                        List<CommonQueryResult> commonQueryResults = (List<CommonQueryResult>)checkReport.Data.tableData;
                        decimal diff = (decimal.Parse(item.ReportQty) + decimal.Parse(commonQueryResults[0].ReportQty)) - decimal.Parse(item.Qty);
                        if (diff > 0)
                        {
                            result.SetFail(null, "不允许超报工，请重新修改报工数量。工单" + request.MfgOrder + "已报工数量" + commonQueryResults[0].ReportQty);
                            return result;
                        }
                    }
                }
               //调用ERP接口
                string msg = string.Empty;
                var groupData = request.requestData.GroupBy(g => g.MfgOrder);
                foreach (var group in groupData)
                {
                    WorkorderReport workorderReport = new WorkorderReport();
                    workorderReport.MESID = Guid.NewGuid().ToString("N");
                    workorderReport.AUFNR = group.Key;
                    List<AFVGDItem> AFVGD = new List<AFVGDItem>();
                    foreach (var sub in group)
                    {
                            //查找step
                        CommonQueryRequest commonQueryRequest = new CommonQueryRequest();
                        commonQueryRequest.mfgorder = group.Key;
                        commonQueryRequest.spec = sub.Spec;
                        BaseResult<List<ModelingResult>> getStep = new QueryService().GetStepByMfgOrder(commonQueryRequest);
                        if (getStep.Data.Count == 0)
                        {
                            result.SetFail(null, "工单："+ group.Key + "+工序：" + sub.Spec + "找不到工序编码，请检查工艺流程。");
                            return result;
                        }

                        AFVGDItem AItem = new AFVGDItem();
                        AItem.VORNR = getStep.Data.Count > 0 ? getStep.Data[0].CDOName : sub.StepName;
                        AItem.LMNGA = sub.ReportQty;
                        AItem.VGW01 = sub.WorkTime != null ? sub.WorkTime.ToString() : string.Empty;
                        AItem.VGW02 = sub.WorkTime != null ? sub.WorkTime.ToString() : string.Empty;
                        AItem.VGW03 = sub.WorkTime != null ? sub.WorkTime.ToString() : string.Empty;
                        AItem.VGW04 = sub.WorkTime != null ? sub.WorkTime.ToString() : string.Empty;
                        AItem.VGW05 = sub.WorkTime != null ? sub.WorkTime.ToString() : string.Empty;
                        AFVGD.Add(AItem);
                    }
                    workorderReport.AFVGD = AFVGD;
                    BaseResult<ERPInfo> erpResult = ERPRequest(AppSetting.ERPConfig.WORKORDER_REPORT, JsonConvert.SerializeObject(workorderReport));
                    Logger.Info(LoggerType.ERP, JsonConvert.SerializeObject(workorderReport), JsonConvert.SerializeObject(erpResult));

                    if (erpResult.Result == 0)
                    {
                        return result.SetFail(null, "ERP信息：" + erpResult.Message);
                    }
                }
                _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                string serviceName = "W_WorkReportTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputData = oService.InputData();

                ICsiSubentityList details = inputData.SubentityList("WorkReportDetails");
                foreach (var item in request.requestData)
                {
                    ICsiSubentity sub = details.AppendItem();
                    sub.RevisionedObjectField("Spec").SetRef(item.Spec, "", true);
                    sub.NamedObjectField("MfgOrder").SetRef(item.MfgOrder);
                    sub.DataField("ReportQty").SetValue(item.ReportQty);
                    sub.DataField("WorkTime").SetValue(item.WorkTime);
                    sub.DataField("ProductDate").SetFormattedValue(request.txnDate, DataFormats.FormatDate);
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                 }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic SelfInspectionOrderMaint(CommonCDORequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string cdoName = string.Empty;

                if (string.IsNullOrEmpty(request.CDOName))
                {
                    cdoName = DateTime.Now.ToString("yyMMddHHmmss");
                }
                else
                {
                    cdoName = request.CDOName;
                }
                string serviceName = "W_SelfInspectionOrderMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(cdoName);
                oService.Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(cdoName);

                switch (request.Type)
                {
                    case "receive":
                        objectChanges.DataField("status").SetValue(request.Status);
                        break;
                    case "inspection":
                        {
                            objectChanges.DataField("status").SetValue(request.Status);
                            objectChanges.DataField("outcome").SetValue(request.OutCome);
                            //objectChanges.DataField("InspectionDate").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                            //objectChanges.NamedObjectField("Inspector").SetRef(request.User);
                            if (request.requestData != null)
                            {
                                ICsiSubentityList subentityList = objectChanges.SubentityList("W_SelfInspectionMaterials");
                                BaseResult<CustomTableRusult> checkMaterial = new QueryService()
                                 .GetSelfInspectionMaterils(new CommonQueryRequest()
                                 {
                                     CDOName = cdoName
                                 });
                                if (checkMaterial.Data.tableData.Count() > 0)
                                {
                                    for (int i = 0; i < checkMaterial.Data.tableData.Count(); i++)
                                    {
                                        subentityList.DeleteItemByIndex(0);
                                    }
                                }
                                foreach (var item in request.requestData)
                                {
                                    ICsiSubentity sub = subentityList.AppendItem();
                                    sub.NamedObjectField("Container").SetRef(item.Container);
                                    sub.DataField("ItemResult").SetValue(item.ItemResult == "OK" ? "1" : "0");
                                    sub.DataField("Notes").SetValue(item.Notes);
                                }
                            }
                            if (request.CollectDetails != null)
                            {
                                ICsiSubentityList collectDetails = objectChanges.SubentityList("W_FAICollectDetails");
                                foreach (var item in request.CollectDetails)
                                {
                                    if (item.ActualValue != null)
                                    {
                                        foreach (var collect in item.ActualValue)
                                        {
                                            ICsiSubentity sub = collectDetails.AppendItem();
                                            sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                            sub.DataField("ActualValue").SetValue(collect.ActualValue);
                                            sub.DataField("ItemResult").SetValue(item.ItemResult == "OK" ? "1" : "0");
                                            sub.DataField("Notes").SetValue(item.Notes);
                                            sub.DataField("Defect").SetValue(collect.Defect);
                                           // sub.DataField("INSPECTIONTYPE").SetValue(item.InspectionType);
                                            sub.DataField("PRODUCT").SetValue(item.Product);
                                            sub.DataField("SPEC").SetValue(item.Spec);
                                            sub.DataField("INSPECTIONPOINTCONTENT").SetValue(item.InspectionPointContent);
                                            sub.DataField("InspectionPointType").SetValue(item.InspectionPointType);
                                            sub.DataField("INSPECTIONPOINTTOOL").SetValue(item.InspectionTool);
                                            sub.DataField("LOWERLIMT").SetValue(item.LowerLimit);
                                            sub.DataField("UPPERLIMT").SetValue(item.UpperLimit);
                                        }
                                    }
                                    else
                                    {
                                        ICsiSubentity sub = collectDetails.AppendItem();
                                        sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                        sub.DataField("ItemResult").SetValue(item.ItemResult == "OK" ? "1" : "0");
                                        sub.DataField("Notes").SetValue(item.Notes);
                                        sub.DataField("Defect").SetValue(item.Defect);
                                        sub.DataField("PRODUCT").SetValue(item.Product);
                                        sub.DataField("SPEC").SetValue(item.Spec);
                                        sub.DataField("INSPECTIONPOINTCONTENT").SetValue(item.InspectionPointContent);
                                        sub.DataField("InspectionPointType").SetValue(item.InspectionPointType);
                                        sub.DataField("INSPECTIONPOINTTOOL").SetValue(item.InspectionTool);
                                        sub.DataField("LOWERLIMT").SetValue(item.LowerLimit);
                                        sub.DataField("UPPERLIMT").SetValue(item.UpperLimit);
                                    }
                                }
                            }
                        }
                        break;
                    default:
                        {
                            objectChanges.DataField("CreateTime").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                            objectChanges.NamedObjectField("Employee").SetRef(request.User);
                            objectChanges.NamedObjectField("MfgOrder").SetRef(request.MfgOrder);
                            objectChanges.NamedObjectField("Resource").SetRef(request.Resource);
                            objectChanges.RevisionedObjectField("Spec").SetRef(request.Spec, "", true);
                            objectChanges.NamedObjectField("ProductionTiming").SetRef(request.ProductionTiming);
                            objectChanges.DataField("InspectionType").SetValue(request.InspectionType);

                            if (request.requestData != null)
                            {
                                ICsiSubentityList subentityList = objectChanges.SubentityList("W_SelfInspectionMaterials");

                                foreach (var item in request.requestData)
                                {
                                    ICsiSubentity sub = subentityList.AppendItem();
                                    sub.NamedObjectField("Container").SetRef(item.Container);
                                }
                            }

                            if (request.requestData2 != null)
                            {
                                ICsiSubentityList subentityList = objectChanges.SubentityList("W_SelfInspectionDetails");

                                foreach (var item in request.requestData2)
                                {
                                    ICsiSubentity sub = subentityList.AppendItem();
                                    sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                    sub.DataField("InspectionPointContent").SetValue(item.InspectionPointContent);
                                    sub.DataField("InspectionTool").SetValue(item.InspectionTool);
                                    sub.DataField("Notes").SetValue(item.Notes);
                                    sub.DataField("ActualValue").SetValue(item.ItemResult);
                                    sub.DataField("Defect").SetValue(item.Defect);
                                }
                            }

                            if (request.CollectDetails != null)
                            {
                                ICsiSubentityList collectDetails = objectChanges.SubentityList("W_FAICollectDetails");
                                foreach (var item in request.CollectDetails)
                                {
                                    if (item.ActualValue != null)
                                    {
                                        foreach (var collect in item.ActualValue)
                                        {
                                            ICsiSubentity sub = collectDetails.AppendItem();
                                            sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                            sub.DataField("ActualValue").SetValue(collect.ActualValue);
                                            sub.DataField("ItemResult").SetValue(item.ItemResult == "OK" ? "1" : "0");
                                            sub.DataField("Notes").SetValue(item.Notes);
                                            sub.DataField("Defect").SetValue(collect.Defect);
                                            // sub.DataField("INSPECTIONTYPE").SetValue(item.InspectionType);
                                            sub.DataField("PRODUCT").SetValue(item.Product);
                                            sub.DataField("SPEC").SetValue(item.Spec);
                                            sub.DataField("INSPECTIONPOINTCONTENT").SetValue(item.InspectionPointContent);
                                            sub.DataField("InspectionPointType").SetValue(item.InspectionPointType);
                                            sub.DataField("INSPECTIONPOINTTOOL").SetValue(item.InspectionTool);
                                            sub.DataField("LOWERLIMT").SetValue(item.LowerLimit);
                                            sub.DataField("UPPERLIMT").SetValue(item.UpperLimit);
                                            sub.DataField("W_InspectionType").SetValue("1");
                                        }
                                    }
                                    else
                                    {
                                        ICsiSubentity sub = collectDetails.AppendItem();
                                        sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                        sub.DataField("ItemResult").SetValue(item.ItemResult == "OK" ? "1" : "0");
                                        sub.DataField("Notes").SetValue(item.Notes);
                                        sub.DataField("Defect").SetValue(item.Defect);
                                        sub.DataField("PRODUCT").SetValue(item.Product);
                                        sub.DataField("SPEC").SetValue(item.Spec);
                                        sub.DataField("INSPECTIONPOINTCONTENT").SetValue(item.InspectionPointContent);
                                        sub.DataField("InspectionPointType").SetValue(item.InspectionPointType);
                                        sub.DataField("INSPECTIONPOINTTOOL").SetValue(item.InspectionTool);
                                        sub.DataField("LOWERLIMT").SetValue(item.LowerLimit);
                                        sub.DataField("UPPERLIMT").SetValue(item.UpperLimit);
                                        sub.DataField("W_InspectionType").SetValue("1");
                                    }
                                }
                            }
                        }
                        break;
                }



                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic MfgOrderStartTxn(MfgOrderStartRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            string sqlStr = string.Empty;
            try
            {
                if (string.IsNullOrEmpty(request.Resource))
                {
                    return result.SetFail(null, "设备为空！");
                }
                if (string.IsNullOrEmpty(request.MfgOrder))
                {
                    return result.SetFail(null, "工单为空！");
                }
                //检查是否有开始记录
                sqlStr = $@"select w.w_mfgorderstartname from W_MfgOrderStart w 
                inner join resourcedef r on r.resourceId = w.resourceid
                inner join mfgOrder mf on mf.Mfgorderid = w.Mfgorderid
                where r.resourcename = :re
                and mf.Mfgordername = :mfgorder";

                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("re", request.Resource);
                parameters.Add("mfgorder", request.MfgOrder);
                dynamic queryResult = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, parameters);
                if (queryResult != null)
                {
                    return result.SetFail(null, "该工单该机台已经有开始记录");
                }

                //检查机台是否有物料
                sqlStr = @$"select mf.mfgordername,PB.Productname PName,MB.Productname MName,sb.Specname,pm.specbaseid,pm.specid from mfgOrder mf
                    inner join W_MfgOrderMaterialList ml on ml.mfgorderid = mf.mfgorderid
                    inner join product M on M.Productid = ml.productid
                    inner join productBase MB on Mb.Productbaseid = M.productBaseid
                    inner join product p on p.productid = mf.productid or mf.productbaseid = p.productbaseid
                    inner join productbase PB on PB.Productbaseid = p.productbaseid
                    inner join workflow w on w.workflowid = mf.isworkflowid or w.workflowbaseid = mf.isworkflowbaseid
                    inner join workflowstep  ws on ws.workflowid = w.workflowid and ws.sequence = 1
                    inner join spec s on s.specid = ws.specid or s.specbaseid = ws.specbaseid
                    inner join specbase sb on sb.specbaseid = s.specbaseid
                    inner join BOM B on B.Bomid = p.bomid or p.bombaseid = b.bombaseid
                    inner join bombase bb on bb.bombaseid = b.bombaseid
                    inner join ProductMaterialListItem pm on pm.bomid = B.Bomid and pm.productid = ml.productid and (s.specid = pm.specid or s.specbaseid = pm.specbaseid)
                            where mf.mfgordername = :mfgOrder";
                parameters = new DynamicParameters();
                parameters.Add("mfgOrder", request.MfgOrder);
                List<dynamic> querylist = DBServerProvider.SqlDapper.QueryDynamicList(sqlStr, parameters);
                if (querylist.Count > 0)
                {
                    sqlStr = @$"
                    select PB.Productname,RE.Resourcename from A_ResourceComponents RC
                    JOIN ResourceDef RE ON RC.ResourceID = RE.ResourceID
                    INNER JOIN Container CN ON RC.FromContainerId = CN.ContainerId
                    inner JOIN PRODUCT P ON CN.PRODUCTID = P.PRODUCTID
                    inner JOIN PRODUCTBASE PB ON P.PRODUCTBASEID = PB.PRODUCTBASEID
                    where RE.Resourcename = :resourceName
                    ";
                    parameters = new DynamicParameters();
                    parameters.Add("resourceName", request.Resource);
                    List<dynamic> Mlist = DBServerProvider.SqlDapper.QueryDynamicList(sqlStr, parameters);
                    if (Mlist.Count > 0)
                    {
                        //检查机台是否缺少物料
                        foreach (var item in Mlist)
                        {
                            var check = querylist.Where(x => x.PName == item.Productname).FirstOrDefault();
                            if (check == null)
                            {
                                return result.SetFail(null, "机台物料" + item.Productname + "缺少，请检查！");
                            }
                        }
                    }
                }
                dynamic chengeList = null;
                //卡控是否有换线记录
                if (request.IsCheck)
                {
                    sqlStr = $@"
                     SELECT 
                    mo.MfgOrderName as mfgOrder,
                    mo.qty quantity,
                    pb.ProductName as productCode,
                    p.description as productDesc,
                    re.resourcename as machineNo
                 FROM W_LineChangeAndSetup lcs
                LEFT JOIN Employee cr ON lcs.Creatorid = cr.EmployeeId
                LEFT JOIN Employee er ON lcs.Completerid = er.EmployeeId
                INNER JOIN ResourceDef re on lcs.ResourceId = re.ResourceId
                INNER JOIN MfgOrder mo on lcs.WMfgOrderId = mo.MfgOrderId 
                LEFT JOIN A_EmployeeGroup eg on mo.W_EmployeeGroupId = eg.EmployeeGroupId
                LEFT JOIN Department dm ON lcs.DepartmentId = dm.DepartmentId
                LEFT JOIN ResourceDef tl on mo.W_ToolId = tl.ResourceId
                LEFT JOIN Product p ON p.ProductId = REPLACE(mo.ProductBaseId, '0000000000000000', mo.ProductId)
                LEFT JOIN ProductBase pb ON p.ProductBaseId = pb.ProductBaseId 
                    OR pb.ProductBaseId = REPLACE(mo.ProductId, '0000000000000000', mo.ProductBaseId)
                LEFT JOIN W_StandardTime st on lcs.ResourceId = st.ResourceId 
                    AND (p.ProductId = st.ProductId OR pb.RevOfRcdId = st.ProductId)
                WHERE 1=1 
                AND (lcs.status = 0 OR lcs.status is NULL)
                and mo.mfgordername = :mfgOrder
                and re.resourcename = :resource
";
                    parameters = new DynamicParameters();
                    parameters.Add("mfgOrder", request.MfgOrder);
                    parameters.Add("resource", request.Resource);
                    chengeList = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, parameters);
                }


                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "W_MfgOrderStartTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.NamedObjectField("Resource").SetRef(request.Resource);
                inputDataSync.NamedObjectField("W_ExcessMfgOrder").SetRef(request.MfgOrder);
                inputDataSync.NamedObjectField("W_Employee").SetRef(request.User);
                inputDataSync.DataField("Materials").SetValue(request.Materials);

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {

                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic PackingManagement(CommonCDORequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string serviceName = "W_PackingManagementTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputData = oService.InputData();
                inputData.NamedObjectField("Container").SetRef(request.Container);
                inputData.DataField("Qty").SetValue(request.Qty);

                ICsiSubentityList details = inputData.SubentityList("PackingManagementDetail");
                foreach (var item in request.requestData)
                {
                    ICsiSubentity sub = details.AppendItem();
                    sub.NamedObjectField("InnerLabelID").SetRef(item.InnerLabelId);
                    sub.DataField("Qty").SetValue(item.InnerQty);
                    sub.DataField("PackingQty").SetValue(item.PackingQty);
                }
                ICsiSubentityList outDetails = inputData.SubentityList("PackingManagementDetailOut");
                foreach (var item in request.requestData2)
                {
                    ICsiSubentity sub = outDetails.AppendItem();
                    sub.NamedObjectField("InnerLabelID").SetRef(item.InnerLabelId);
                    sub.DataField("Qty").SetValue(item.InnerQty);
                    sub.DataField("PackingQty").SetValue(item.PackingQty);
                    sub.DataField("IsOut").SetValue("True");
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic IPQCOrderMaint(CommonCDORequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                string cdoName = string.Empty;

                if (string.IsNullOrEmpty(request.CDOName))
                {
                    cdoName = DateTime.Now.ToString("yyMMddHHmmss");
                }
                else
                {
                    cdoName = request.CDOName;
                }
                string serviceName = "W_IPQCOrderMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(cdoName);
                oService.Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(cdoName);

                switch (request.Type)
                {
                    case "new":
                        objectChanges.NamedObjectField("mfgorder").SetRef(request.MfgOrder);
                        objectChanges.NamedObjectField("Creator").SetRef(request.User);
                        objectChanges.NamedObjectField("IPQCDuration").SetRef(request.IPQCDuration);
                        objectChanges.NamedObjectField("Resource").SetRef(request.Resource);
                        objectChanges.NamedObjectField("EmployeeGroup").SetRef(request.EmployeeGroup);
                        objectChanges.DataField("CavityCount").SetValue(request.CavityCount);
                        objectChanges.DataField("CreateTime").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                        objectChanges.DataField("status").SetValue(request.Status);
                        break;
                    case "inspection":
                        {
                            objectChanges.NamedObjectField("Inspector").SetRef(request.User);
                            objectChanges.DataField("InspectionTime").SetFormattedValue(DateTime.Now.ToString(), DataFormats.FormatDate);
                            objectChanges.DataField("status").SetValue(request.Status);
                            objectChanges.DataField("OutCome").SetValue(request.OutCome);
                            
                            // objectChanges.DataField("InspectResult").SetValue(request.InspectResult);
                            if (request.CollectDetails != null)
                            {
                                ICsiSubentityList collectDetails = objectChanges.SubentityList("W_IPQCCollectDetails");
                                //判断是否存在Material Item，有先删除
                                int count = new QueryService().GetIPQCCollectDetails(cdoName).Data.Count;
                                if (count > 0)
                                {
                                    for (int i = 0; i < count; i++)
                                    {
                                        collectDetails.DeleteItemByIndex(0);
                                    }
                                }
                                foreach (var item in request.CollectDetails)
                                {
                                    if (item.ActualValue != null)
                                    {
                                        foreach (var collect in item.ActualValue)
                                        {
                                            ICsiSubentity sub = collectDetails.AppendItem();
                                            sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                            sub.DataField("ActualValue").SetValue(collect.ActualValue);
                                            sub.DataField("ActualValue1").SetValue(collect.ActualValue1);
                                            sub.DataField("ActualValue2").SetValue(collect.ActualValue2);
                                            sub.DataField("ActualValue3").SetValue(collect.ActualValue3);
                                            sub.DataField("ActualValue4").SetValue(collect.ActualValue4);
                                            sub.DataField("ActualValue5").SetValue(collect.ActualValue5);
                                            sub.DataField("ItemResult").SetValue(string.IsNullOrEmpty(item.ItemResult) ? "" : (item.ItemResult == "OK" ? "1" : "0"));
                                            sub.DataField("Notes").SetValue(item.Notes);
                                        }

                                    }
                                  
                                    else
                                    {
                                        ICsiSubentity sub = collectDetails.AppendItem();
                                        sub.DataField("InspectionPoint").SetValue(item.InspectionPoint);
                                        sub.DataField("ItemResult").SetValue(string.IsNullOrEmpty(item.ItemResult) ? "" : (item.ItemResult == "OK" ? "1" : "0"));
                                        sub.DataField("Notes").SetValue(item.Notes);
                                        sub.DataField("ItemResult1").SetValue(string.IsNullOrEmpty(item.ItemResult1) ? "0" : (item.ItemResult1 == "OK" ? "1" : "2"));
                                        sub.DataField("Comments1").SetValue(item.Comments1);
                                        sub.DataField("ItemResult2").SetValue(string.IsNullOrEmpty(item.ItemResult2) ? "0" : (item.ItemResult2 == "OK" ? "1" : "2"));
                                        sub.DataField("Comments2").SetValue(item.Comments2);
                                        sub.DataField("ItemResult3").SetValue(string.IsNullOrEmpty(item.ItemResult3) ? "0" : (item.ItemResult3 == "OK" ? "1" : "2"));
                                        sub.DataField("Comments3").SetValue(item.Comments3);
                                        sub.DataField("ItemResult4").SetValue(string.IsNullOrEmpty(item.ItemResult4) ? "0" : (item.ItemResult4 == "OK" ? "1" : "2"));
                                        sub.DataField("Comments4").SetValue(item.Comments4);
                                        sub.DataField("ItemResult5").SetValue(string.IsNullOrEmpty(item.ItemResult5) ? "0" : (item.ItemResult5 == "OK" ? "1" : "2"));
                                        sub.DataField("Comments5").SetValue(item.Comments5);
                                        sub.DataField("Defect").SetValue(item.Defect);
                                    }
                                }
                            }
                        }
                        break;
                    default:
                        break;
                }



                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic ERPPrint(string url, string json)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                string user = AppSetting.ERPConfig.UserName;
                string psw = AppSetting.ERPConfig.Password;
                HttpClientHelper httpClientHelper = new HttpClientHelper();
                httpClientHelper.Url = url;
                httpClientHelper.RequestJson = json;
                var postResult = httpClientHelper.PostRequest(user, psw);

                if (postResult.Result == 1)
                {
                    PrintInfo info = JsonConvert.DeserializeObject<PrintInfo>(postResult.Data); 
                    if (info.MSG_TYPE == "S")
                    {
                        result.SetSuccess(null, info.MSG_CONTENT);
                    }
                    else
                    {
                        result.SetFail(null, info.MSG_CONTENT);
                    }
                }
                else
                {
                    result.SetFail(null, postResult.Message);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic ERPReprint(PrintLabelRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                string url = string.Empty;
                string json = string.Empty;
                string user = AppSetting.ERPConfig.UserName;
                string psw = AppSetting.ERPConfig.Password;
                HttpClientHelper httpClientHelper = new HttpClientHelper();

                switch (request.Type)
                {
                    case "start":
                        url = AppSetting.ERPConfig.COR_PRINT;
                        //获取批次信息
                        BaseResult<CommonQueryResult> containerInfo = new QueryService().GetStartLabelInfo(request.Container);
                        ERPRequest<StartLabel> printRequest = new ERPRequest<StartLabel>();
                        printRequest.data = new List<StartLabel>();
                        StartLabel label = new StartLabel();
                        label.ZEINR = request.Container;
                        label.AUFNR = containerInfo.Data.MfgOrder;
                        label.MATNR = containerInfo.Data.Product;
                        label.BZ_MENGE = containerInfo.Data.MfgOrderQty;
                        label.MECAP = containerInfo.Data.Qty;
                        label.STEXT = containerInfo.Data.StepName;
                        label.MEINS = containerInfo.Data.Uom;
                        label.RSRC = request.Printer;
                        printRequest.data.Add(label);
                        json = JsonConvert.SerializeObject(printRequest);
                        break;
                    case "materialLabel":
                        
                        break;
                    default:
                        break;
                }

                httpClientHelper.Url = url;
                httpClientHelper.RequestJson = json;
                var postResult = httpClientHelper.PostRequest(user, psw);

                if (postResult.Result == 1)
                {
                    PrintInfo info = JsonConvert.DeserializeObject<PrintInfo>(postResult.Data);
                    if (info.MSG_TYPE == "S")
                    {
                        result.SetSuccess(null, info.MSG_CONTENT);
                    }
                    else
                    {
                        result.SetFail(null, info.MSG_CONTENT);
                    }
                }
                else
                {
                    result.SetFail(null, postResult.Message);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
            /// 生产派工
            /// </summary>
        public dynamic DispatchTxn(DispatchTaskRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                if (!string.IsNullOrEmpty(request.Password))
                {
                    _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                }
                if (request.Action == 1|| request.Action == 3)
                {
                    if(string.IsNullOrEmpty(request.MfgOrder)||string.IsNullOrEmpty(request.Resource)||string.IsNullOrEmpty(request.Tool))
                    {
                        return result.SetFail(null, "工单,设备，模具 不能为空！");
                    }
                    else if (!IsCheckResource(request.Resource, "Resource"))
                    {
                        return result.SetFail(null, $@"{request.Resource}没有找到设备数据");
                    }
                    else if (!IsCheckResource(request.Tool,"Tool"))
                    {
                        return result.SetFail(null, $@"{request.Tool}没有找到模具数据");
                    }
                    else if (!string.IsNullOrEmpty(request.WaitTool)&&!IsCheckResource(request.WaitTool, "Tool"))
                    {
                        return result.SetFail(null, $@"{request.WaitTool}没有找到等待模具数据");
                    }
                }
                else if (request.Action == 2)
                {
                    if (request.DispatchOrderHistory ==null||string.IsNullOrEmpty(request.DispatchOrderHistory.DispatchHistoryId))
                    {
                        return result.SetFail(null, "等待模具不能为空！");
                    }
                }
                string serviceName = "W_DispatchingOrder";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();

                ICsiObject _inputData = oService.InputData();
                _inputData.DataField("Action").SetValue(request.Action.ToString());
                _inputData.NamedObjectField("MfgOrder").SetRef(request.MfgOrder);
                _inputData.DataField("Priority").SetValue(request.Priority);
                _inputData.NamedObjectField("Resource").SetRef(request.Resource);
                _inputData.NamedObjectField("Tool").SetRef(request.Tool);
                _inputData.NamedObjectField("W_WaitTool").SetRef(request.WaitTool);
                _inputData.DataField("Comments").SetValue(request.Comment);
                if (request.DispatchOrderHistory!= null&&(request.Action ==2|| request.Action == 3))
                    _inputData.ObjectField("W_DispatchOrderHistory").SetObjectId(request.DispatchOrderHistory.DispatchHistoryId);
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }


        /// <summary>
        /// ERP请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="json"></param>
        /// <returns></returns>
        public dynamic ERPRequest(string url, string json)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<ERPInfo> result = new BaseResult<ERPInfo>();

            try
            {
                string user = AppSetting.ERPConfig.UserName;
                string psw = AppSetting.ERPConfig.Password;
                HttpClientHelper httpClientHelper = new HttpClientHelper();
                httpClientHelper.Url = url;
                httpClientHelper.RequestJson = json;
                var postResult = httpClientHelper.PostRequest(user, psw);

                if (postResult.Result == 1)
                {
                    ERPInfo info = JsonConvert.DeserializeObject<ERPInfo>(postResult.Data);
                    if (info.TYPE == "S")
                    {
                        result.SetSuccess(info, info.MSG);
                    }
                    else
                    {
                        result.SetFail(null, info.MSG);
                    }
                }
                else
                {
                    result.SetFail(null, postResult.Message);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        /// <summary>
        /// 检查设备是否存在
        /// </summary>
        /// <param name="resource"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public bool IsCheckResource(string resource, string type)
        {
            bool isCheck = false;
            DynamicParameters dynamic = new DynamicParameters();
            string sqlStr = $@"select RE.RESOURCENAME,cd.cdoName from RESOURCEDEF  re
inner JOIN CDODEFINITION cd on cd.CDODEFID = re.CDOTYPEID
where 1=1  
and RE.RESOURCENAME = :RESOURCENAME
AND cd.cdoName = :cdo";
            dynamic.Add("RESOURCENAME", resource);
            dynamic.Add("cdo", type);
            dynamic queryResult = DBServerProvider.SqlDapper.QueryDynamicFirst(sqlStr, dynamic);
            if (queryResult != null)
            {
                isCheck = true;
            }
            return isCheck;
        }

        public dynamic ERPMaterialIssue()
        {
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                //查询物料消耗数据
                string msg = string.Empty;
                BaseResult<List<CommonQueryResult>> baseResult = new QueryService().GetMaterialIssue();
                if (baseResult.Result == 1 && baseResult.Data.Count > 0)
                {
                    for (int i = 0; i < baseResult.Data.Count; i++)
                    {
                        MaterialConsumption materialConsumption = new MaterialConsumption();
                        List<MaterialConsumptionItem> materialConsumptions = new List<MaterialConsumptionItem>();
                        MaterialConsumptionItem materialConsumptionItem = new MaterialConsumptionItem();
                        materialConsumptionItem.AUFNR = baseResult.Data[i].MfgOrder;
                        materialConsumptionItem.HUIDENT = baseResult.Data[i].Container;
                        //materialConsumptionItem.IDNRK = baseResult.Data[i].Product;
                        materialConsumptionItem.MENGE = baseResult.Data[i].Qty;
                        //materialConsumptionItem.CHARG = baseResult.Data[i].Container;
                        //materialConsumptionItem.LGORT = baseResult.Data[i].InventoryLocation;
                        materialConsumptions.Add(materialConsumptionItem);
                        materialConsumption.MESID = Guid.NewGuid().ToString("N");
                        materialConsumption.ITEM = materialConsumptions;

                        BaseResult<ERPInfo> erpResult = ERPRequest(AppSetting.ERPConfig.MATERIAL_CONSUMPTION, JsonConvert.SerializeObject(materialConsumption));
                        if (erpResult.Result == 0)
                        {
                            new QueryService().SetERPReportResult(2, erpResult.Message, baseResult.Data[i].CDOID);
                            msg += erpResult.Message + Environment.NewLine;
                        }
                        else
                        {
                            new QueryService().SetERPReportResult(1, erpResult.Message, baseResult.Data[i].CDOID);
                            msg += erpResult.Message + "\n";
                        }
                        Logger.Info(LoggerType.ERP, JsonConvert.SerializeObject(materialConsumption), msg);
                    }
                    result.SetSuccess(null, msg);

                }
                else
                {
                    result.SetFail(null, baseResult.Message);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        public dynamic WarehouseReceiptTxn(CommonCDORequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();

            try
            {
                _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                string serviceName = "W_WarehouseReceiptTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();

                switch (request.Type)
                {
                    case "modify":
                        inputData.NamedObjectField("WarehouseReceip").SetRef(request.CDOName);
                        inputData.DataField("Type").SetValue("1");
                        break;
                    case "add":
                        {
                            inputData.NamedObjectField("NumberingRule").SetRef("WarehouseReceiptRule");
                            inputData.DataField("Type").SetValue("0");
                            inputData.DataField("InventoryOrderQty").SetValue(request.InventoryOrderQty);
                            if (request.requestData != null)
                            {
                                ICsiSubentityList subentityList = inputData.SubentityList("WarehouseReceiptDetails");
                                foreach (var item in request.requestData)
                                {
                                    ICsiSubentity sub = subentityList.AppendItem();
                                    sub.NamedObjectField("Container").SetRef(item.Container);
                                    sub.DataField("InventoryQty").SetValue(item.InventoryQty);
                                }
                            }
                        }
                        break;
                }

                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                requestData.RequestField("ReturnReceiptName");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    //调用ERP打印接口
                    ICsiResponseData response = respDoc.GetService().ResponseData();
                    ICsiDataField receipt = (ICsiDataField)response.GetResponseFieldByName("ReturnReceiptName");
                    string receiptName = receipt.GetValue();
                    string url = AppSetting.ERPConfig.RKD_PRINT;
                    ERPRequest<RKD_PRINT> printRequest = new ERPRequest<RKD_PRINT>();
                    printRequest.data = new List<RKD_PRINT>();
                    RKD_PRINT label = new RKD_PRINT();
                    label.ZRKD = receiptName;
                    label.ERDAT = DateTime.Now.Date.ToString("yyyyMMdd");
                    label.LGORT = request.InventoryLocation;
                    label.AUFNR = request.requestData[0].MfgOrder;
                    label.MATNR = request.requestData[0].Product;
                    label.WEMNG = request.InventoryOrderQty;
                    label.MEINS = request.requestData[0].Uom;
                    label.ERNAM = request.User;
                    label.RSRC = request.Printer;
                    printRequest.data.Add(label);

                    BaseResult<string> printResult = ERPPrint(url, JsonConvert.SerializeObject(printRequest));
                    if (printResult.Result == 0)
                        result.SetFail(null, "ERP信息：" + printResult.Message);
                    else
                        result.SetSuccess(null, "ERP信息：" + printResult.Message);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }

        public dynamic TurnoverBoxTxn(TurnoverBoxRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                string url = AppSetting.ERPConfig.INBOUND_INSPECTION;
                if (string.IsNullOrEmpty(request.User) || string.IsNullOrEmpty(request.Password))
                {
                    return result.SetFail(null, "用户名或密码不能为空！");
                }
                if (string.IsNullOrEmpty(request.InspectResult.ToString()))
                {
                    return result.SetFail(null, "周转箱状态不能为空");
                }
                if (request.TurnoverBoxList.Count <= 0)
                {
                    return result.SetFail(null, "内箱不能为空！");
                }
                if (string.IsNullOrEmpty(request.W_WarehouseReceipt))
                {
                    return result.SetFail(null, "周转箱不能为空！");
                }

                if (request.InspectResult == 1)
                {
                    //result.SetSuccess(null, submitResult.ResultMsg);
                    // 调用ERP入库报检接口

                    //ERPRequest<InboundInspection> InboundRequest = new ERPRequest<InboundInspection>();
                    //InboundRequest.INBOUND_INSPECTION = new InboundInspection();
                    InboundInspection label = new InboundInspection();
                    label.MESID = Guid.NewGuid().ToString("N");
                    label.ZMESRKD = request.W_WarehouseReceipt;
                    label.AUFNR = request.TurnoverBoxList[0].MfgOrder;
                    label.MENGE = request.TurnoverBoxList.Sum(x => x.Qty);//TurnoverBoxList 中 qty的总数量
                    label.LGORT = request.TurnoverBoxList[0].Location;
                    label.ITEM = new List<BOXINFO>();
                    //foreach 中index++
                    int index = 1;
                    foreach (var item in request.TurnoverBoxList)
                    {
                        BOXINFO itemData = new BOXINFO
                        {
                            ZEILE = index, //行号
                            BOXNUM = item.TurnoverBox, //周转箱号
                            HUIDENT = item.InnerLabelID, //内箱条码
                        };
                        label.ITEM.Add(itemData);
                        index++;
                    }

                    BaseResult<string> Result = ERPInventory(url, label);
                    if (Result.Result == 0)
                       return result.SetFail(null, Result.Message);
                }
                else if (request.InspectResult == 2)
                {
                    if (string.IsNullOrEmpty(request.PRUEFLOS))
                    {
                        return result.SetFail(null, "PRUEFLOS 不能为空！");
                    }
                    url = AppSetting.ERPConfig.FQC_INSPECTION;
                    FQCInspectionRequest fQCInspection = new FQCInspectionRequest();

                    fQCInspection.MESID = Guid.NewGuid().ToString("N");
                    fQCInspection.PRUEFLOS = request.PRUEFLOS;
                    BaseResult<string> result1 =  ERPFQCReport(url, fQCInspection);
                    if (result1.Result == 0)
                    {
                        return result.SetFail(null, result1.Message);
                    }
                }

                _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                string serviceName = "W_TurnoverBoxTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                inputData.NamedObjectField("W_WarehouseReceipt").SetRef(request.W_WarehouseReceipt);
                inputData.DataField("InspectResult").SetValue(request.InspectResult.ToString());
                inputData.DataField("Comments").SetValue(request.Comments);
                if (!string.IsNullOrEmpty(request.HoldReason))
                {
                    inputData.DataField("HoldeReasons").SetValue(request.HoldReason);
                }
                var list = inputData.SubentityList("W_TurnoverBoxServiceDetails");
                foreach (var item in request.TurnoverBoxList)
                {
                    var obj = list.AppendItem();
                    obj.NamedObjectField("Employee").SetRef(request.User);
                    obj.DataField("InspectResult").SetValue(request.InspectResult.ToString());
                    obj.NamedObjectField("MfgOrder").SetRef(item.MfgOrder);
                    obj.DataField("TurnoverBox").SetValue(item.TurnoverBox);
                    obj.NamedObjectField("W_InnerLabelID").SetRef(item.InnerLabelID);
                    obj.DataField("W_InventoryLocation").SetValue(item.Location);
                }
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                  result.SetSuccess(null, submitResult.ResultMsg);

                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            
            return result;

        }

        public dynamic ProductMaintanceTxn(ProductMaintanceRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                if (string.IsNullOrEmpty(request.User) || string.IsNullOrEmpty(request.Password))
                {
                    return result.SetFail(null, "用户名或密码不能为空！");
                }
                _camstarXmlClient = new CamstarXmlClient(request.User, request.Password.Trim().DecryptDES(AppSetting.Secret.User));
                string serviceName = "W_ProductMaintenanceTxn";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputData = oService.InputData();
                inputData.ContainerField("Container").SetRef(request.Container, null);
                inputData.NamedObjectField("JobCauseCode").SetRef(request.JobCauseCode);
                inputData.NamedObjectField("W_MaintenanceMethod").SetRef(request.W_MaintenanceMethod);
                inputData.DataField("Improvement").SetValue(request.Improvement);
                inputData.DataField("WorkingHours").SetValue(request.WorkingHours);
                inputData.DataField("BadPosition").SetValue(request.BadPosition);
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();
                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");
                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null,ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 修改入库单中的ERP_PRUEFLOS
        /// </summary>
        /// <param name="warahouseRecipt"></param>
        /// <param name="ERP_PRUEFLOS"></param>
        /// <returns></returns>
        public dynamic WarehoseReceiptMaint(string warahouseRecipt,string ERP_PRUEFLOS)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            try
            {
                _camstarXmlClient = new CamstarXmlClient(AppSetting.Camstar.UserName, AppSetting.Camstar.Password.Trim());
                string serviceName = "W_WarehouseReceiptMaint";
                _camstarXmlClient.InitializeSession();
                _camstarXmlClient.CreateDocumentandService(serviceName + "Doc", serviceName);
                ICsiService oService = _camstarXmlClient.GetService();
                ICsiObject inputDataSync = oService.InputData();
                inputDataSync.DataField("SyncName").SetValue(warahouseRecipt);
                oService.Perform("Sync");

                ICsiObject inputData = oService.InputData();
                ICsiNamedObject objectChanges = inputData.NamedObjectField("ObjectChanges");
                objectChanges.DataField("Name").SetValue(warahouseRecipt);
                objectChanges.DataField("ERP_PRUEFLOS").SetValue(ERP_PRUEFLOS);
                oService.SetExecute();
                ICsiRequestData requestData;
                requestData = oService.RequestData();
                requestData.RequestField("CompletionMsg");
                ICsiDocument respDoc = _camstarXmlClient.GetDocument().Submit();

                submitResult = _camstarXmlClient.CheckForErrors(respDoc);
                _camstarXmlClient.CleanUp(serviceName + "Doc");

                if (submitResult.ResultCode == "1")
                {
                    result.SetSuccess(null, submitResult.ResultMsg);
                }
                else
                {
                    result.SetFail(null, submitResult.ResultMsg);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }
            return result;
        }

        /// <summary>
        /// ERP入库报检请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="json"></param>
        public dynamic ERPInventory (string url, InboundInspection InboundRequest)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            string json = JsonConvert.SerializeObject(InboundRequest);
            
            try
            {
                string user = AppSetting.ERPConfig.UserName;
                string psw = AppSetting.ERPConfig.Password;
                HttpClientHelper httpClientHelper = new HttpClientHelper();
                httpClientHelper.Url = url;
                httpClientHelper.RequestJson = json;
                
                var postResult = httpClientHelper.PostRequest(user, psw);
                Logger.Info(LoggerType.ERP, json, JsonConvert.SerializeObject(postResult));
                if (postResult.Result == 1)
                {
                    ERPInfo info = JsonConvert.DeserializeObject<ERPInfo>(postResult.Data);
                    if (info.TYPE == "S")
                    {
                       return WarehoseReceiptMaint(InboundRequest.ZMESRKD, info.PRUEFLOS);
                    }
                    else
                    {
                        result.SetFail(null, "ERP返回错误："+info.MSG);
                    }
                }
                else
                {
                    result.SetFail(null, "调用ERP接口异常：" + postResult.Message);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }



        /// <summary>
        /// ERPFQC报检请求
        /// </summary>
        /// <param name="url"></param>
        /// <param name="json"></param>
        public dynamic ERPFQCReport(string url, FQCInspectionRequest request)
        {
            SubmitResult submitResult = new SubmitResult();
            BaseResult<string> result = new BaseResult<string>();
            string json = JsonConvert.SerializeObject(request);
            try
            {
                string user = AppSetting.ERPConfig.UserName;
                string psw = AppSetting.ERPConfig.Password;
                HttpClientHelper httpClientHelper = new HttpClientHelper();
                httpClientHelper.Url = url;
                httpClientHelper.RequestJson = json;
                var postResult = httpClientHelper.PostRequest(user, psw);
                Logger.Info(LoggerType.ERP, json, JsonConvert.SerializeObject(postResult));
                if (postResult.Result == 1)
                {
                    ERPInfo info = JsonConvert.DeserializeObject<ERPInfo>(postResult.Data);
                    if (info.TYPE == "S")
                    {
                        return result.SetSuccess(null, info.MSG);
                    }
                    else
                    {
                        result.SetFail(null, "ERP返回错误：" + info.MSG);
                    }
                }
                else
                {
                    result.SetFail(null, "ERP调用异常：" + postResult.Message);
                }
            }
            catch (Exception ex)
            {
                result.SetFail(null, ex.Message);
            }

            return result;
        }
    }
}
