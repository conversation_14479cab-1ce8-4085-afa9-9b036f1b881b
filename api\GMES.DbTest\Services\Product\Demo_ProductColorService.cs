/*
 *Author：GTECH
 *
 *代码由框架生成,此处任何更改都可能导致被代码生成器覆盖
 *所有业务编写全部应在Partial文件夹下Demo_ProductColorService与IDemo_ProductColorService中编写
 */
using GMES.DbTest.IRepositories;
using GMES.DbTest.IServices;
using GMES.Core.BaseProvider;
using GMES.Core.Extensions.AutofacManager;
using GMES.Entity.DomainModels;

namespace GMES.DbTest.Services
{
    public partial class Demo_ProductColorService : ServiceBase<Demo_ProductColor, IDemo_ProductColorRepository>
    , IDemo_ProductColorService, IDependency
    {
    public Demo_ProductColorService(IDemo_ProductColorRepository repository)
    : base(repository)
    {
    Init(repository);
    }
    public static IDemo_ProductColorService Instance
    {
      get { return AutofacContainerModule.GetService<IDemo_ProductColorService>(); } }
    }
 }
