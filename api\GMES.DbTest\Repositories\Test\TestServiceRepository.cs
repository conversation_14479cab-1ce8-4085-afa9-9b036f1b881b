﻿/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Repository提供数据库操作，如果要增加数据库操作请在当前目录下Partial文件夹TestServiceRepository编写代码
 */
using GMES.DbTest.IRepositories;
using GMES.Core.BaseProvider;
using GMES.Core.EFDbContext;
using GMES.Core.Extensions.AutofacManager;
using GMES.Entity.DomainModels;

namespace GMES.DbTest.Repositories
{
    public partial class TestServiceRepository : RepositoryBase<TestService> , ITestServiceRepository
    {
    public TestServiceRepository(ServiceDbContext dbContext)
    : base(dbContext)
    {

    }
    public static ITestServiceRepository Instance
    {
      get {  return AutofacContainerModule.GetService<ITestServiceRepository>(); } }
    }
}
