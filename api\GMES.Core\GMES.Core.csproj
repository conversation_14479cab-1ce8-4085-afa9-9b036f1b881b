﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;EF1001;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;EF1001;</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="IRepositories\**" />
    <Compile Remove="IServices\**" />
    <Compile Remove="Repositories\**" />
    <Compile Remove="Services\API\**" />
    <Compile Remove="Services\Base\**" />
    <EmbeddedResource Remove="IRepositories\**" />
    <EmbeddedResource Remove="IServices\**" />
    <EmbeddedResource Remove="Repositories\**" />
    <EmbeddedResource Remove="Services\API\**" />
    <EmbeddedResource Remove="Services\Base\**" />
    <None Remove="IRepositories\**" />
    <None Remove="IServices\**" />
    <None Remove="Repositories\**" />
    <None Remove="Services\API\**" />
    <None Remove="Services\Base\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="6.0.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="AutoMapper" Version="6.2.2" />
    <PackageReference Include="CSRedisCore" Version="3.6.5" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="EFCore.BulkExtensions" Version="2.2.2" />
    <PackageReference Include="EfCore.SqlServer2008Query" Version="6.0.0" />
    <PackageReference Include="EPPlus.Core" Version="1.5.4" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Formatters.Json" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="2.1.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="6.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Redis.Core" Version="1.0.3" />
    <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="3.1.0" />
    <PackageReference Include="Npgsql" Version="6.0.11" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.0" />
    <PackageReference Include="Oracle.EntityFrameworkCore" Version="6.21.4" />
    <!--<PackageReference Include="MySql.Data" Version="8.0.13" />-->
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.0" />
    <PackageReference Include="Quartz" Version="3.4.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.7" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.35.0" />
    <PackageReference Include="ZKWeb.System.Drawing" Version="4.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GMES.Entity\GMES.Entity.csproj" />
  </ItemGroup>

</Project>
