﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName></SccProjectName>
    <SccProvider></SccProvider>
    <SccAuxPath></SccAuxPath>
    <SccLocalPath></SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ApplicationIcon />
    <OutputType>Library</OutputType>
    <StartupObject />
    <AssemblyName>GMES.Builder</AssemblyName>
    <RootNamespace>GMES.Builder</RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="6.0.0" />
    <PackageReference Include="AutoMapper" Version="6.2.2" />
    <PackageReference Include="EPPlus.Core" Version="1.5.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GMES.Core\GMES.Core.csproj" />
    <ProjectReference Include="..\GMES.Entity\GMES.Entity.csproj" />
  </ItemGroup>

</Project>
