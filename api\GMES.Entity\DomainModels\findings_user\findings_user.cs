﻿/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GMES.Entity.SystemModels;

namespace GMES.Entity.DomainModels
{
    [Entity(TableCnName = "疑似用户",TableName = "findings_user",DBServer = "ServiceDbContext")]
    public partial class findings_user:ServiceEntity
    {
        /// <summary>
       ///ID
       /// </summary>
       [Key]
       [Display(Name ="ID")]
       [MaxLength(36)]
       [Column(TypeName="uniqueidentifier")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public Guid id { get; set; }

       /// <summary>
       ///Account Name
       /// </summary>
       [Display(Name ="Account Name")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       public string account { get; set; }

       /// <summary>
       ///Full name
       /// </summary>
       [Display(Name ="Full name")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       public string name { get; set; }

       /// <summary>
       ///Find Time
       /// </summary>
       [Display(Name ="Find Time")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? findtime { get; set; }

       /// <summary>
       ///Update At
       /// </summary>
       [Display(Name ="Update At")]
       [Column(TypeName="datetime")]
       [Editable(true)]
       public DateTime? updateat { get; set; }

       /// <summary>
       ///Comments
       /// </summary>
       [Display(Name ="Comments")]
       [MaxLength(255)]
       [Column(TypeName="nvarchar(255)")]
       [Editable(true)]
       public string lastdetails { get; set; }

       
    }
}
