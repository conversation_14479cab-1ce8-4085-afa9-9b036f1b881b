﻿using Autofac;
using Autofac.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.EntityFrameworkCore;
using GMES.Entity.DomainModels;
using GMES.Core.EFDbContext;
using GMES.Core.Configuration;
using GMES.Core.Language;
using GMES.Core.Utilities;
using GMES.Core.Extensions;
using GMES.Core.DBManage;
using GMES.Core.Const;

namespace GMES.Core.Language
{
    public static class LanguageExtensions
    {
        public static IApplicationBuilder UseLanguagePack(this IApplicationBuilder app)
        {
            using (var autofacContainer = app.ApplicationServices.GetAutofacRoot().BeginLifetimeScope())
            {
                using SysDbContext context = autofacContainer.Resolve<SysDbContext>();
                try
                {
                    context.CreateLanguagePack();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                }
                return app;
            }
        }
        public static void CreateLanguagePack(this BaseDbContext context)
        {
            //FormattableString sql = null;

            var lang = context.Set<Sys_Language>()
                .Select(s => new
                {
                    s.IsPackageContent,
                    defaultKey = s.defaultKey.Trim(),
                    s.ZHCN,//简体中文
                    s.ZHTW,//繁体中文
                    s.English,//英文
                    s.Spanish,//西班牙语
                    s.French,//法语
                    s.Arabic,  //阿拉伯语
                    s.Russian //俄语
                }).ToList()
            .GroupBy(x => x.defaultKey)
            .Select(s => new
            {
                defaultKey= s.Key.Trim(),
                ZHCN = s.Max(m => m.ZHCN)?.Trim(),
                ZHTW = s.Max(m => m.ZHTW)?.Trim(),
                English = s.Max(m => m.English)?.Trim(),
                French = s.Max(m => m.French)?.Trim(),
                Spanish = s.Max(m => m.Spanish)?.Trim(),
                Arabic = s.Max(m => m.Arabic)?.Trim(),
                Russian = s.Max(m => m.Russian)?.Trim()
            }).ToList().GroupBy(x => x.defaultKey).Select(s => new
            {
                defaultKey = s.Key.Trim(),
                ZHCN = s.Max(m => m.ZHCN)?.Trim(),
                ZHTW = s.Max(m => m.ZHTW)?.Trim(),
                English = s.Max(m => m.English)?.Trim(),
                French = s.Max(m => m.French)?.Trim(),
                Spanish = s.Max(m => m.Spanish)?.Trim(),
                Arabic = s.Max(m => m.Arabic)?.Trim(),
                Russian = s.Max(m => m.Russian)?.Trim()
            }).ToList();

            string path = AppSetting.FullStaticPath;

            var lang_zhtw = lang.Where(c => !string.IsNullOrEmpty(c.ZHTW))
            .Select(s => new KeyValuePair<string, string>(s.defaultKey, s.ZHTW))
            .ToDictionary(x => x.Key, x => x.Value);
            LanguageContainer.Add(LangConst.繁体中文, lang_zhtw);

            FileHelper.WriteFile(path, LangConst.繁体中文 + ".js", $"{lang_zhtw.Serialize()}");

            var lang_chinese = lang.Where(c => !string.IsNullOrEmpty(c.ZHCN))
            .Select(s => new KeyValuePair<string, string>(s.defaultKey, s.ZHCN))
            .ToDictionary(x => x.Key, x => x.Value);
            LanguageContainer.Add(LangConst.简体中文, lang_chinese);

            FileHelper.WriteFile(path, LangConst.简体中文 + ".js", $"{lang_chinese.Serialize()}");

            var lang_english = lang.Where(c => !string.IsNullOrEmpty(c.English))
            .Select(s => new KeyValuePair<string, string>(s.defaultKey, s.English))
            .ToDictionary(x => x.Key, x => x.Value);
            LanguageContainer.Add(LangConst.英文,lang_english);

            FileHelper.WriteFile(path, LangConst.英文 + ".js", $"{lang_english.Serialize()}");

            var lang_french = lang.Where(c => !string.IsNullOrEmpty(c.French))
              .Select(s => new KeyValuePair<string, string>(s.defaultKey, s.French))
              .ToDictionary(x => x.Key, x => x.Value);
            LanguageContainer.Add(LangConst.法语, lang_french);

            FileHelper.WriteFile(path, LangConst.法语 + ".js", $"{lang_french.Serialize()}");


            var lang_spanish = lang.Where(c => !string.IsNullOrEmpty(c.Spanish))
            .Select(s => new KeyValuePair<string, string>(s.defaultKey, s.Spanish))
            .ToDictionary(x => x.Key, x => x.Value);
            LanguageContainer.Add(LangConst.西班牙语,lang_spanish);

            FileHelper.WriteFile(path, LangConst.西班牙语 + ".js", $"{lang_spanish.Serialize()}");

            var lang_arabic = lang.Where(c => !string.IsNullOrEmpty(c.Arabic))
            .Select(s => new KeyValuePair<string, string>(s.defaultKey, s.Arabic))
            .ToDictionary(x => x.Key, x => x.Value);
            LanguageContainer.Add(LangConst.阿拉伯语, lang_arabic);

            FileHelper.WriteFile(path, LangConst.阿拉伯语 + ".js", $"{lang_arabic.Serialize()}");

            var lang_ru = lang.Where(c => !string.IsNullOrEmpty(c.Russian))
            .Select(s => new KeyValuePair<string, string>(s.defaultKey, s.Russian))
            .ToDictionary(x => x.Key, x => x.Value);
            LanguageContainer.Add(LangConst.俄语, lang_ru);

            FileHelper.WriteFile(path, LangConst.俄语 + ".js", $"{lang_ru.Serialize()}");

            //其他语言包，请在此处接着添加

        }
    }

}
