﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702;CS8618;CS8603;CS8600;CS8629;CS8625;</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702;CS8618;CS8603;CS8600;CS8629;CS8625;</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus.Core" Version="1.5.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\GMES.Core\GMES.Core.csproj" />
    <ProjectReference Include="..\GMES.Entity\GMES.Entity.csproj" />
  </ItemGroup>

</Project>
