﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using GMES.Core.Extensions;
using GMES.Core.Configuration;

namespace GMES.Core.Quartz
{
    public static class HttpManager
    {

        public static async Task<string> SendAsync(this IHttpClientFactory httpClientFactory,
            HttpMethod method,
            string url,
            string postData = null,
            int timeOut = 180,
            Dictionary<string, string> headers = null)
        {
            var client = httpClientFactory.CreateClient();
            //var content = new StringContent(postData ?? "");
            //var request = new HttpRequestMessage(method, url)
            //{
            //    Content = content
            //};

            HttpContent httpContent = new StringContent(new { a = 1, b = 2 }.Serialize(), Encoding.UTF8, "application/json");

            headers ??= new Dictionary<string, string>();
            headers.TryAdd(QuartzAuthorization.Key, QuartzAuthorization.AccessKey);
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                    //request.Headers.Add(header.Key, header.Value);
                }
            }
            try
            {
                client.Timeout = TimeSpan.FromSeconds(timeOut);
                HttpResponseMessage response = null;
                if (method == HttpMethod.Post)
                {
                    response = await client.PostAsync(url, httpContent);
                }
                else
                {
                    response = await client.GetAsync(url);
                }
                string result;
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    result = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    result = response.Serialize();
                }
                Console.WriteLine("返回：" + result);
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                QuartzFileHelper.Error($"http请求异常，url:{url},{ex.Message + ex.StackTrace}");
                return ex.Message;
            }
        }

        public static async Task<string> GetdataFromWebAPI(this IHttpClientFactory httpClientFactory, string ApiAddress, string ApiPath, string JsonBody)
        {
            //string url = AppSetting.HandHttp + "MES/SmsSendInstoLineWarehouse";
            string url = ApiAddress + ApiPath;
            string bizmessage = "";
            string HttpStatusCode = "";
            try
            {

                var httpClientHandler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (message, certificate2, arg3, arg4) => true  //忽略掉证书异常
                };

                using (HttpClient httpClient = new HttpClient(httpClientHandler))
                {
                    StringContent httpContent = new StringContent(
                        JsonBody,
                        Encoding.UTF8,
                        "application/json"
                    );
                    HttpResponseMessage response = httpClient.PostAsync(url, httpContent).Result;

                    HttpStatusCode = response.StatusCode.ToString();

                    bizmessage = response.Content.ReadAsStringAsync().Result;
                }
            }
            catch (Exception ex)
            {
            }

            return bizmessage;
        }


        public static async Task<string> PostXML(this IHttpClientFactory httpClientFactory, string ApiAddress, string XmlBody)
        {
            //string url = http://***********:3100/Integration/Printer/Execute";
            string url = ApiAddress;
            string bizmessage = "";
            string HttpStatusCode = "";
            try
            {

                var httpClientHandler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (message, certificate2, arg3, arg4) => true  //忽略掉证书异常
                };

                using (HttpClient httpClient = new HttpClient(httpClientHandler))
                {
                    StringContent httpContent = new StringContent(
                        XmlBody,
                        Encoding.UTF8,
                        "application/xml"
                    );
                    HttpResponseMessage response = httpClient.PostAsync(url, httpContent).Result;

                    HttpStatusCode = response.StatusCode.ToString();

                    bizmessage = response.Content.ReadAsStringAsync().Result;
                }
            }
            catch (Exception ex)
            {
            }

            return bizmessage;
        }
    }
}
